// Example usage of API with internationalization

// ===== SERVER COMPONENT EXAMPLE =====
// src/app/[locale]/blog/page.tsx
import { BlogService } from '@/services/apis/blog';
import { getLocale } from 'next-intl/server';

export default async function BlogPage() {
  const locale = await getLocale();
  
  const { success, data: posts, error } = await BlogService.getPosts({
    page: 1,
    per_page: 10,
    locale: locale // Pass current locale
  });

  if (!success || !posts) {
    return <div>Error loading blog posts</div>;
  }

  return (
    <div>
      <h1>Blog Posts ({locale})</h1>
      {posts.map((post: any) => (
        <article key={post.id}>
          <h2>{post.name}</h2>
          <p>{post.excerpt}</p>
        </article>
      ))}
    </div>
  );
}

// ===== CLIENT COMPONENT EXAMPLE =====
// src/components/BlogList.tsx
'use client';

import { useEffect, useState } from 'react';
import { useApiClient } from '@/hooks/useApiClient';
import { useLocale } from 'next-intl';

export default function BlogList() {
  const apiClient = useApiClient();
  const locale = useLocale();
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        // API call will automatically include current locale
        const response = await apiClient.get('posts', { 
          page: 1, 
          per_page: 10 
        }, { type: 'blog' });
        
        if (response.data) {
          setPosts(response.data);
        }
      } catch (error) {
        console.error('Error fetching posts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, [apiClient]);

  if (loading) return <div>Loading...</div>;

  return (
    <div>
      <h2>Blog Posts ({locale})</h2>
      {posts.map((post: any) => (
        <article key={post.id}>
          <h3>{post.name}</h3>
          <p>{post.excerpt}</p>
        </article>
      ))}
    </div>
  );
}

// ===== DIRECT SERVER FETCHER USAGE =====
// src/app/[locale]/blog/[slug]/page.tsx
import { serverFetcher } from '@/api/server';
import { getLocale } from 'next-intl/server';

export default async function BlogPostPage({ params }: { params: { slug: string } }) {
  const locale = await getLocale();

  // Direct server fetcher usage with locale
  const { data: post, error } = await serverFetcher({
    endpoint: `posts/${params.slug}`,
    type: 'blog',
    locale: locale // Explicitly pass locale
  });

  if (error || !post) {
    return <div>Post not found</div>;
  }

  return (
    <article>
      <h1>{post.name}</h1>
      <div dangerouslySetInnerHTML={{ __html: post.content }} />
    </article>
  );
}

// ===== CLIENT COMPONENT WITH MANUAL LOCALE =====
// src/components/PostDetail.tsx
'use client';

import { useEffect, useState } from 'react';
import { useApiClient } from '@/hooks/useApiClient';

interface PostDetailProps {
  slug: string;
  forceLocale?: string; // Optional: force specific locale
}

export default function PostDetail({ slug, forceLocale }: PostDetailProps) {
  const apiClient = useApiClient();
  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPost = async () => {
      try {
        let response;
        
        if (forceLocale) {
          // Use direct fetcher with specific locale (client-side)
          const { fetcher } = await import('@/api');
          response = await fetcher({
            endpoint: `posts/${slug}`,
            type: 'blog',
            locale: forceLocale
          });
        } else {
          // Use apiClient with current locale
          response = await apiClient.get(`posts/${slug}`, {}, { type: 'blog' });
        }
        
        if (response.data) {
          setPost(response.data);
        }
      } catch (error) {
        console.error('Error fetching post:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPost();
  }, [slug, forceLocale, apiClient]);

  if (loading) return <div>Loading...</div>;
  if (!post) return <div>Post not found</div>;

  return (
    <article>
      <h1>{(post as any).name}</h1>
      <div dangerouslySetInnerHTML={{ __html: (post as any).content }} />
    </article>
  );
}

// ===== API SERVICE USAGE =====
// src/services/custom-blog.ts
import { BlogService } from '@/services/apis/blog';

export class CustomBlogService {
  static async getEnglishPosts() {
    return BlogService.getPosts({
      page: 1,
      per_page: 5,
      locale: 'en' // Force English
    });
  }

  static async getVietnamesePosts() {
    return BlogService.getPosts({
      page: 1,
      per_page: 5,
      locale: 'vi' // Force Vietnamese
    });
  }

  static async getPostInBothLanguages(slug: string) {
    const [enPost, viPost] = await Promise.all([
      BlogService.getPostBySlug(slug, 'en'),
      BlogService.getPostBySlug(slug, 'vi')
    ]);

    return {
      english: enPost.data,
      vietnamese: viPost.data
    };
  }
}

// ===== FORM SUBMISSION EXAMPLE =====
// src/components/ContactForm.tsx
'use client';

import { useState } from 'react';
import { useApiClient } from '@/hooks/useApiClient';

export default function ContactForm() {
  const apiClient = useApiClient();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // POST request will automatically include current locale
      const response = await apiClient.post('contact', formData, {}, { type: 'home' });
      
      if (response.data) {
        alert('Message sent successfully!');
        setFormData({ name: '', email: '', message: '' });
      }
    } catch (error) {
      console.error('Error sending message:', error);
      alert('Failed to send message');
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="text"
        placeholder="Name"
        value={formData.name}
        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
      />
      <input
        type="email"
        placeholder="Email"
        value={formData.email}
        onChange={(e) => setFormData({ ...formData, email: e.target.value })}
      />
      <textarea
        placeholder="Message"
        value={formData.message}
        onChange={(e) => setFormData({ ...formData, message: e.target.value })}
      />
      <button type="submit">Send Message</button>
    </form>
  );
}
