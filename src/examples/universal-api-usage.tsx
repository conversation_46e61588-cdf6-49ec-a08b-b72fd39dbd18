// Universal API Usage Examples

// ===== SERVER COMPONENT EXAMPLE =====
// src/app/[locale]/blog/page.tsx
import { BlogService } from '@/services/apis/blog';
import { serverApiClient } from '@/lib/server-api-client';
import { getLocale } from 'next-intl/server';

export default async function BlogPage() {
  const locale = await getLocale();
  
  // Option 1: Use BlogService (recommended)
  const { success, data: posts, error } = await BlogService.getPosts({
    page: 1,
    per_page: 10,
    locale: locale
  });

  // Option 2: Use serverApiClient directly
  const directResponse = await serverApiClient.get('posts', {
    page: 1,
    per_page: 10
  }, {
    type: 'blog',
    locale: locale
  });

  if (!success || !posts) {
    return <div>Error loading blog posts</div>;
  }

  return (
    <div>
      <h1>Blog Posts ({locale})</h1>
      {posts.map((post: any) => (
        <article key={post.id}>
          <h2>{post.name}</h2>
          <p>{post.excerpt}</p>
        </article>
      ))}
    </div>
  );
}

// ===== CLIENT COMPONENT EXAMPLE =====
// src/components/BlogList.tsx
'use client';

import { useEffect, useState } from 'react';
import { useApiClient } from '@/hooks/useApiClient';
import { apiClient } from '@/lib/api-client';
import { useLocale } from 'next-intl';

export default function BlogList() {
  // Option 1: Use useApiClient hook (recommended)
  const api = useApiClient({ defaultType: 'blog' });
  const locale = useLocale();
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPosts = async () => {
      try {
        // Automatically includes current locale
        const response = await api.get('posts', { 
          page: 1, 
          per_page: 10 
        });
        
        if (response.success && response.data) {
          setPosts(response.data);
        }
      } catch (error) {
        console.error('Error fetching posts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPosts();
  }, [api]);

  // Option 2: Use apiClient directly
  const fetchPostsDirectly = async () => {
    const response = await apiClient.get('posts', {
      page: 1,
      per_page: 10
    }, {
      type: 'blog',
      locale: locale // Explicitly pass locale
    });
    
    if (response.success) {
      setPosts(response.data || []);
    }
  };

  if (loading) return <div>Loading...</div>;

  return (
    <div>
      <h2>Blog Posts ({locale})</h2>
      <button onClick={fetchPostsDirectly}>Refresh Posts</button>
      {posts.map((post: any) => (
        <article key={post.id}>
          <h3>{post.name}</h3>
          <p>{post.excerpt}</p>
        </article>
      ))}
    </div>
  );
}

// ===== FORM SUBMISSION EXAMPLE =====
// src/components/ContactForm.tsx
'use client';

import { useState } from 'react';
import { useApiClient } from '@/hooks/useApiClient';

export default function ContactForm() {
  const api = useApiClient({ defaultType: 'main' }); // Use main API
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    
    try {
      // POST request will automatically include current locale
      const response = await api.post('contact', formData);
      
      if (response.success) {
        alert('Message sent successfully!');
        setFormData({ name: '', email: '', message: '' });
      } else {
        alert(`Error: ${response.error?.message || 'Failed to send message'}`);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      alert('Failed to send message');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="text"
        placeholder="Name"
        value={formData.name}
        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
        required
      />
      <input
        type="email"
        placeholder="Email"
        value={formData.email}
        onChange={(e) => setFormData({ ...formData, email: e.target.value })}
        required
      />
      <textarea
        placeholder="Message"
        value={formData.message}
        onChange={(e) => setFormData({ ...formData, message: e.target.value })}
        required
      />
      <button type="submit" disabled={submitting}>
        {submitting ? 'Sending...' : 'Send Message'}
      </button>
    </form>
  );
}

// ===== MULTI-LANGUAGE CONTENT EXAMPLE =====
// src/components/MultiLanguagePost.tsx
'use client';

import { useEffect, useState } from 'react';
import { apiClient } from '@/lib/api-client';

interface MultiLanguagePostProps {
  slug: string;
}

export default function MultiLanguagePost({ slug }: MultiLanguagePostProps) {
  const [posts, setPosts] = useState<{
    en: any;
    vi: any;
  }>({ en: null, vi: null });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPostsInBothLanguages = async () => {
      try {
        // Fetch post in both languages
        const [enResponse, viResponse] = await Promise.all([
          apiClient.get(`posts/${slug}`, {}, { type: 'blog', locale: 'en' }),
          apiClient.get(`posts/${slug}`, {}, { type: 'blog', locale: 'vi' })
        ]);

        setPosts({
          en: enResponse.success ? enResponse.data : null,
          vi: viResponse.success ? viResponse.data : null
        });
      } catch (error) {
        console.error('Error fetching posts:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPostsInBothLanguages();
  }, [slug]);

  if (loading) return <div>Loading...</div>;

  return (
    <div style={{ display: 'flex', gap: '2rem' }}>
      <div style={{ flex: 1 }}>
        <h2>English Version</h2>
        {posts.en ? (
          <article>
            <h3>{posts.en.name}</h3>
            <p>{posts.en.excerpt}</p>
          </article>
        ) : (
          <p>English version not available</p>
        )}
      </div>
      
      <div style={{ flex: 1 }}>
        <h2>Vietnamese Version</h2>
        {posts.vi ? (
          <article>
            <h3>{posts.vi.name}</h3>
            <p>{posts.vi.excerpt}</p>
          </article>
        ) : (
          <p>Vietnamese version not available</p>
        )}
      </div>
    </div>
  );
}

// ===== CUSTOM API SERVICE EXAMPLE =====
// src/services/custom-api.ts
import { apiClient } from '@/lib/api-client';
import { serverApiClient } from '@/lib/server-api-client';

export class CustomApiService {
  // Client-side method
  static async getProductsClient(locale?: string) {
    return apiClient.get('products', {}, {
      type: 'main',
      locale: locale
    });
  }

  // Server-side method
  static async getProductsServer(locale?: string) {
    return serverApiClient.get('products', {}, {
      type: 'main',
      locale: locale
    });
  }

  // Multi-language method
  static async getProductsInAllLanguages() {
    const [enProducts, viProducts] = await Promise.all([
      apiClient.get('products', {}, { type: 'main', locale: 'en' }),
      apiClient.get('products', {}, { type: 'main', locale: 'vi' })
    ]);

    return {
      english: enProducts.success ? enProducts.data : null,
      vietnamese: viProducts.success ? viProducts.data : null
    };
  }
}
