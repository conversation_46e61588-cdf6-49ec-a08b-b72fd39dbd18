"use client";

import { createTranslator, useTranslations } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';

export const locales = ['vi', 'en'] as const;

// Define your pathnames mapping as needed
export const pathnames = {
  '/about': {
    en: '/about',
    vi: '/about',
  },
};

// Export navigation helpers directly from 'next/navigation'
export { useRouter, usePathname };
