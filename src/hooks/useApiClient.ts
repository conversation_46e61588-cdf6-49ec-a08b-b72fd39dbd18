'use client';

import { useLocale } from 'next-intl';
import { useMemo } from 'react';
import { FRONT_END_DOMAIN, ROUTE_API_HANDLER } from '@/utils/env';

interface ApiClientOptions {
  type?: string;
  headers?: HeadersInit;
}

class ClientApiClient {
  private locale: string;
  private baseUrl: string;

  constructor(locale: string) {
    this.locale = locale;
    this.baseUrl = `${FRONT_END_DOMAIN}/${ROUTE_API_HANDLER}`;
  }

  private buildUrlWithParams(endpoint: string, params?: Record<string, any>): string {
    const url = `${this.baseUrl}/${endpoint}`;
    
    if (!params) {
      return `${url}?lang=${this.locale}`;
    }

    const query = new URLSearchParams();
    
    // Add language parameter first
    query.append('lang', this.locale);
    
    // Add other parameters
    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null) {
        query.append(key, String(params[key]));
      }
    }
    
    return `${url}?${query}`;
  }

  async get<T>(
    endpoint: string, 
    params?: Record<string, any>, 
    options: ApiClientOptions = {}
  ): Promise<{ data: T | null; error: any; meta: any }> {
    try {
      const url = this.buildUrlWithParams(endpoint, params);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept-Language': this.locale,
          typeEndpoint: options.type || 'blog',
          ...options.headers,
        },
      });

      const json = await response.json();
      
      if (!response.ok) {
        return { data: null, error: { message: json.message || 'API Error' }, meta: null };
      }

      return { data: json?.data ?? null, meta: json?.meta ?? null, error: null };
    } catch (error) {
      return { data: null, error, meta: null };
    }
  }

  async post<T>(
    endpoint: string, 
    data?: any, 
    params?: Record<string, any>,
    options: ApiClientOptions = {}
  ): Promise<{ data: T | null; error: any; meta: any }> {
    try {
      const url = this.buildUrlWithParams(endpoint, params);
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept-Language': this.locale,
          typeEndpoint: options.type || 'blog',
          ...options.headers,
        },
        body: data ? JSON.stringify(data) : undefined,
      });

      const json = await response.json();
      
      if (!response.ok) {
        return { data: null, error: { message: json.message || 'API Error' }, meta: null };
      }

      return { data: json?.data ?? null, meta: json?.meta ?? null, error: null };
    } catch (error) {
      return { data: null, error, meta: null };
    }
  }

  async put<T>(
    endpoint: string, 
    data?: any, 
    params?: Record<string, any>,
    options: ApiClientOptions = {}
  ): Promise<{ data: T | null; error: any; meta: any }> {
    try {
      const url = this.buildUrlWithParams(endpoint, params);
      
      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Accept-Language': this.locale,
          typeEndpoint: options.type || 'blog',
          ...options.headers,
        },
        body: data ? JSON.stringify(data) : undefined,
      });

      const json = await response.json();
      
      if (!response.ok) {
        return { data: null, error: { message: json.message || 'API Error' }, meta: null };
      }

      return { data: json?.data ?? null, meta: json?.meta ?? null, error: null };
    } catch (error) {
      return { data: null, error, meta: null };
    }
  }

  async delete<T>(
    endpoint: string, 
    params?: Record<string, any>,
    options: ApiClientOptions = {}
  ): Promise<{ data: T | null; error: any; meta: any }> {
    try {
      const url = this.buildUrlWithParams(endpoint, params);
      
      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Accept-Language': this.locale,
          typeEndpoint: options.type || 'blog',
          ...options.headers,
        },
      });

      const json = await response.json();
      
      if (!response.ok) {
        return { data: null, error: { message: json.message || 'API Error' }, meta: null };
      }

      return { data: json?.data ?? null, meta: json?.meta ?? null, error: null };
    } catch (error) {
      return { data: null, error, meta: null };
    }
  }
}

export function useApiClient() {
  const locale = useLocale();
  
  return useMemo(() => new ClientApiClient(locale), [locale]);
}
