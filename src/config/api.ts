// API Configuration
export const API_CONFIG = {
  // Always use direct API calls (no proxy)
  USE_NEXT_API_PROXY: false,

  // Direct API URLs
  BLOG_API_URL: process.env.NEXT_PUBLIC_BLOG_API_URL || 'https://api.cslant.com/api',
  MAIN_API_URL: process.env.NEXT_PUBLIC_MAIN_API_URL || 'https://main-api.cslant.com/api',

  // Default settings
  DEFAULT_TIMEOUT: 10000,
  DEFAULT_LOCALE: 'en',
  SUPPORTED_LOCALES: ['en', 'vi'] as const,
} as const;

export type ApiType = 'blog' | 'main' | 'home';
export type SupportedLocale = typeof API_CONFIG.SUPPORTED_LOCALES[number];

export function getApiBaseUrl(type: ApiType): string {
  // Always use direct API URLs
  switch (type) {
    case 'blog':
      return API_CONFIG.BLOG_API_URL;
    case 'main':
    case 'home':
      return API_CONFIG.MAIN_API_URL;
    default:
      return API_CONFIG.BLOG_API_URL;
  }
}
