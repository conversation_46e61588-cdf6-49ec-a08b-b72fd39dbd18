// API Configuration
export const API_CONFIG = {
  // Toggle between direct API calls or Next.js API proxy
  USE_NEXT_API_PROXY: process.env.NEXT_PUBLIC_USE_API_PROXY === 'true',
  
  // Direct API URLs
  BLOG_API_URL: process.env.NEXT_PUBLIC_BLOG_API_URL || 'https://api.cslant.com/api',
  MAIN_API_URL: process.env.NEXT_PUBLIC_MAIN_API_URL || 'https://main-api.cslant.com/api',
  
  // Next.js API proxy URLs (fallback)
  NEXT_API_PROXY_URL: process.env.NEXT_PUBLIC_FRONTEND_URL || 'http://localhost:3000',
  PROXY_ROUTE: 'cpi',
  
  // Default settings
  DEFAULT_TIMEOUT: 10000,
  DEFAULT_LOCALE: 'en',
  SUPPORTED_LOCALES: ['en', 'vi'] as const,
} as const;

export type ApiType = 'blog' | 'main' | 'home';
export type SupportedLocale = typeof API_CONFIG.SUPPORTED_LOCALES[number];

export function getApiBaseUrl(type: ApiType): string {
  if (API_CONFIG.USE_NEXT_API_PROXY) {
    // Use Next.js API proxy
    return `${API_CONFIG.NEXT_API_PROXY_URL}/${API_CONFIG.PROXY_ROUTE}`;
  }
  
  // Use direct API URLs
  switch (type) {
    case 'blog':
      return API_CONFIG.BLOG_API_URL;
    case 'main':
    case 'home':
      return API_CONFIG.MAIN_API_URL;
    default:
      return API_CONFIG.BLOG_API_URL;
  }
}
