import { API_CONFIG, getApiBaseUrl, type ApiType, type SupportedLocale } from '@/config/api';

export interface ApiRequestOptions {
  endpoint: string;
  type?: ApiType;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  params?: Record<string, any>;
  data?: any;
  headers?: Record<string, string>;
  locale?: string;
  timeout?: number;
}

export interface ApiResponse<T = any> {
  data: T | null;
  error: any;
  meta?: any;
  success: boolean;
}

class UniversalApiClient {
  private getLocaleFromEnvironment(): string {
    // Check if we're in browser
    if (typeof window !== 'undefined') {
      // Client-side: get from cookie
      const cookies = document.cookie.split(';');
      const localeCookie = cookies.find(cookie => 
        cookie.trim().startsWith('cslant_locale=')
      );
      if (localeCookie) {
        return localeCookie.split('=')[1].trim();
      }
    }
    
    // Server-side or fallback
    return API_CONFIG.DEFAULT_LOCALE;
  }

  private buildUrl(endpoint: string, type: ApiType, params?: Record<string, any>): string {
    const baseUrl = getApiBaseUrl(type);
    
    if (API_CONFIG.USE_NEXT_API_PROXY) {
      // Next.js proxy format: /cpi/endpoint?params
      const url = new URL(`${baseUrl}/${endpoint}`, window?.location?.origin || API_CONFIG.NEXT_API_PROXY_URL);
      
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            url.searchParams.append(key, String(value));
          }
        });
      }
      
      return url.toString();
    } else {
      // Direct API format: https://api.domain.com/api/endpoint?params
      const url = new URL(endpoint, baseUrl);
      
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            url.searchParams.append(key, String(value));
          }
        });
      }
      
      return url.toString();
    }
  }

  private buildHeaders(options: ApiRequestOptions): Record<string, string> {
    const locale = options.locale || this.getLocaleFromEnvironment();
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Accept-Language': locale,
      ...options.headers,
    };

    // Add special headers for Next.js proxy
    if (API_CONFIG.USE_NEXT_API_PROXY) {
      headers['typeEndpoint'] = options.type || 'blog';
    }

    return headers;
  }

  async request<T = any>(options: ApiRequestOptions): Promise<ApiResponse<T>> {
    const {
      endpoint,
      type = 'blog',
      method = 'GET',
      params = {},
      data,
      timeout = API_CONFIG.DEFAULT_TIMEOUT,
      locale
    } = options;

    try {
      // Add language parameter to params
      const paramsWithLang = {
        ...params,
        lang: locale || this.getLocaleFromEnvironment(),
      };

      const url = this.buildUrl(endpoint, type, paramsWithLang);
      const headers = this.buildHeaders(options);

      console.log(`[ApiClient] ${method} ${url}`);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(url, {
        method,
        headers,
        body: data ? JSON.stringify(data) : undefined,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          data: null,
          error: {
            status: response.status,
            message: errorData.message || `HTTP ${response.status}: ${response.statusText}`,
            details: errorData
          },
          success: false,
        };
      }

      const result = await response.json();
      
      return {
        data: result?.data ?? result,
        meta: result?.meta,
        error: null,
        success: true,
      };

    } catch (error: any) {
      console.error(`[ApiClient] Error:`, error);
      
      return {
        data: null,
        error: {
          message: error.message || 'Network error',
          details: error
        },
        success: false,
      };
    }
  }

  // Convenience methods
  async get<T = any>(endpoint: string, params?: Record<string, any>, options?: Partial<ApiRequestOptions>): Promise<ApiResponse<T>> {
    return this.request<T>({
      endpoint,
      method: 'GET',
      params,
      ...options,
    });
  }

  async post<T = any>(endpoint: string, data?: any, options?: Partial<ApiRequestOptions>): Promise<ApiResponse<T>> {
    return this.request<T>({
      endpoint,
      method: 'POST',
      data,
      ...options,
    });
  }

  async put<T = any>(endpoint: string, data?: any, options?: Partial<ApiRequestOptions>): Promise<ApiResponse<T>> {
    return this.request<T>({
      endpoint,
      method: 'PUT',
      data,
      ...options,
    });
  }

  async delete<T = any>(endpoint: string, options?: Partial<ApiRequestOptions>): Promise<ApiResponse<T>> {
    return this.request<T>({
      endpoint,
      method: 'DELETE',
      ...options,
    });
  }

  async patch<T = any>(endpoint: string, data?: any, options?: Partial<ApiRequestOptions>): Promise<ApiResponse<T>> {
    return this.request<T>({
      endpoint,
      method: 'PATCH',
      data,
      ...options,
    });
  }
}

// Export singleton instance
export const apiClient = new UniversalApiClient();
export default apiClient;
