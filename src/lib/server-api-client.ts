import { API_CONFIG, getApiBaseUrl, type ApiType } from '@/config/api';
import type { ApiRequestOptions, ApiResponse } from '@/lib/api-client';

class ServerApiClient {
  private async getServerLocale(): Promise<string> {
    try {
      // Try to get locale from next-intl
      const { getLocale } = await import('next-intl/server');
      return await getLocale();
    } catch {
      try {
        // Fallback: get from headers
        const { headers } = await import('next/headers');
        const headersList = await headers();
        return headersList.get('x-locale') || API_CONFIG.DEFAULT_LOCALE;
      } catch {
        return API_CONFIG.DEFAULT_LOCALE;
      }
    }
  }

  private buildUrl(endpoint: string, type: ApiType, params?: Record<string, any>): string {
    const baseUrl = getApiBaseUrl(type);

    // Direct API format: https://api.domain.com/api/endpoint?params
    const url = new URL(endpoint, baseUrl);

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value));
        }
      });
    }

    return url.toString();
  }

  private async buildHeaders(options: ApiRequestOptions): Promise<Record<string, string>> {
    const locale = options.locale || await this.getServerLocale();

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Accept-Language': locale,
      ...options.headers,
    };

    return headers;
  }

  async request<T = any>(options: ApiRequestOptions): Promise<ApiResponse<T>> {
    const {
      endpoint,
      type = 'blog',
      method = 'GET',
      params = {},
      data,
      timeout = API_CONFIG.DEFAULT_TIMEOUT,
      locale
    } = options;

    try {
      // Add language parameter to params
      const currentLocale = locale || await this.getServerLocale();
      const paramsWithLang = {
        ...params,
        lang: currentLocale,
      };

      const url = this.buildUrl(endpoint, type, paramsWithLang);
      const headers = await this.buildHeaders(options);

      console.log(`[ServerApiClient] ${method} ${url}`);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(url, {
        method,
        headers,
        body: data ? JSON.stringify(data) : undefined,
        signal: controller.signal,
        // Add cache settings for server-side requests
        ...(method === 'GET' ? { next: { revalidate: 60 } } : {}),
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          data: null,
          error: {
            status: response.status,
            message: errorData.message || `HTTP ${response.status}: ${response.statusText}`,
            details: errorData
          },
          success: false,
        };
      }

      const result = await response.json();
      
      return {
        data: result?.data ?? result,
        meta: result?.meta,
        error: null,
        success: true,
      };

    } catch (error: any) {
      console.error(`[ServerApiClient] Error:`, error);
      
      return {
        data: null,
        error: {
          message: error.message || 'Network error',
          details: error
        },
        success: false,
      };
    }
  }

  // Convenience methods
  async get<T = any>(endpoint: string, params?: Record<string, any>, options?: Partial<ApiRequestOptions>): Promise<ApiResponse<T>> {
    return this.request<T>({
      endpoint,
      method: 'GET',
      params,
      ...options,
    });
  }

  async post<T = any>(endpoint: string, data?: any, options?: Partial<ApiRequestOptions>): Promise<ApiResponse<T>> {
    return this.request<T>({
      endpoint,
      method: 'POST',
      data,
      ...options,
    });
  }

  async put<T = any>(endpoint: string, data?: any, options?: Partial<ApiRequestOptions>): Promise<ApiResponse<T>> {
    return this.request<T>({
      endpoint,
      method: 'PUT',
      data,
      ...options,
    });
  }

  async delete<T = any>(endpoint: string, options?: Partial<ApiRequestOptions>): Promise<ApiResponse<T>> {
    return this.request<T>({
      endpoint,
      method: 'DELETE',
      ...options,
    });
  }

  async patch<T = any>(endpoint: string, data?: any, options?: Partial<ApiRequestOptions>): Promise<ApiResponse<T>> {
    return this.request<T>({
      endpoint,
      method: 'PATCH',
      data,
      ...options,
    });
  }
}

// Export singleton instance
export const serverApiClient = new ServerApiClient();
export default serverApiClient;
