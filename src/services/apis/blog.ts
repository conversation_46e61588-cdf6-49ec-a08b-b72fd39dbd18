import { blogPostApi } from '@/api/blog/post';
import { serverFetcher } from '@/api/server';
import { IBlogPostDetail, TBlogPost } from '@/types/blog';
import { IMetaSEO } from '@/types/app';
import { BLOG_ENDPOINT } from '@/constants/endpoint';

export class BlogService {
  /**
   * Get blog posts with locale support
   * Use this for server components
   */
  static async getPosts(params?: {
    page?: number;
    per_page?: number;
    category?: string;
    tag?: string;
    search?: string;
    locale?: string; // Add locale parameter
  }) {
    try {
      // Use server fetcher for server components
      const response = await serverFetcher({
        endpoint: 'posts',
        params: {
          page: params?.page || 1,
          per_page: params?.per_page || 10,
          ...(params?.category && { category: params.category }),
          ...(params?.tag && { tag: params.tag }),
          ...(params?.search && { search: params.search }),
        },
        type: 'blog',
        locale: params?.locale, // Pass locale
      });

      return {
        success: true,
        data: response.data,
        meta: response.meta,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        meta: null,
        error: error
      };
    }
  }

  /**
   * Get blog post by slug with locale support
   * Use this for server components
   */
  static async getPostBySlug(slug: string, locale?: string): Promise<{
    success: boolean;
    data: TBlogPost | null;
    meta: any;
    error: any;
  }> {
    try {
      console.log(`[BlogService] Fetching blog post with slug: ${slug}, locale: ${locale}`);

      // Use server fetcher for server components
      const response = await serverFetcher<TBlogPost>({
        endpoint: `posts/${slug}`,
        type: 'blog',
        locale: locale, // Pass locale
      });

      console.log(`[BlogService] API Response:`, {
        hasData: !!response.data,
        hasError: !!response.error,
        meta: response.meta
      });

      if (response.error) {
        console.error(`[BlogService] API Error for slug ${slug}:`, response.error);
        return {
          success: false,
          data: null,
          meta: null,
          error: response.error
        };
      }

      if (!response.data) {
        console.warn(`[BlogService] No data returned for slug: ${slug}`);
        return {
          success: false,
          data: null,
          meta: null,
          error: 'No data found'
        };
      }

      console.log(`[BlogService] Successfully fetched blog post: ${response.data.name}`);
      return {
        success: true,
        data: response.data,
        meta: response.meta,
        error: null
      };
    } catch (error) {
      console.error(`[BlogService] Exception when fetching slug ${slug}:`, error);
      return {
        success: false,
        data: null,
        meta: null,
        error: error
      };
    }
  }

  /**
   * Lấy các bài viết liên quan
   */
  static async getRelatedPosts(postId: number, limit: number = 3) {
    try {
      const response = await blogPostApi.get({
        params: {
          related_to: postId,
          limit: limit
        }
      });

      if (response.error) {
        return {
          success: false,
          data: null,
          meta: null,
          error: response.error
        };
      }

      return {
        success: true,
        data: response.data,
        meta: response.meta,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        meta: null,
        error: error
      };
    }
  }

  /**
   * Tạo meta SEO cho blog post
   */
  static generateMetaSEO(post: TBlogPost): IMetaSEO {
    return {
      title: post.name,
      description: post.description,
      keywords: post.tags?.map(tag => tag.name).join(', ') || '',
      image: post.image,
      url: `/blog/${post.slug}`,
      type: 'article',
      author: post.author?.full_name || '',
      publishedTime: post.created_at,
      modifiedTime: post.updated_at
    };
  }
}
