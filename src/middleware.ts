import createIntlMiddleware from "next-intl/middleware";
import { pathnames, locales } from "@/navigation";
import { NextRequest, NextResponse } from "next/server";
import { Lang } from "@/types/page";
import { userAgent } from "next/server";

const getLocaleByUrl = (url: string, defaultLocale: string) => {
  const enRoutes = [
    "/about",
  ];

  if (enRoutes.some((item) => url.startsWith(item))) {
    return "en";
  }

  return defaultLocale;
};

const i18nMiddleware = (request: NextRequest) => {
  const cookieLocale = request.cookies.get("cslant_locale")?.value ?? "en";
  const locale = getLocaleByUrl(request.nextUrl.pathname, cookieLocale);

  const intlMiddleware = createIntlMiddleware({
    defaultLocale: locale as Lang,
    localePrefix: "never",
    localeDetection: false,
    alternateLinks: false,
    locales,
    pathnames,
  });

  request.cookies.set({
    name: "cslant_locale",
    value: locale,
  });
  console.log(request);
  const response = intlMiddleware(request);
  response.cookies.set({
    name: "cslant_locale",
    value: locale,
    path: "/",
    httpOnly: false,
    domain: process.env.NEXT_PUBLIC_COOKIE_DOMAIN,
  });

  return response;
};

const checkDeviceMiddleware = (
  request: NextRequest,
  response: NextResponse,
) => {
  // Check a device
  const { device } = userAgent(request);
  const isMobile = device.type === 'mobile';

  // Add a device type to request
  response.headers.set("x-device-type", isMobile ? "mobile" : "desktop");

  return response;
};

export default async function middleware(request: NextRequest) {
  let response = i18nMiddleware(request);

  // Other middleware here
  response = checkDeviceMiddleware(request, response);
  return response;
}

export const config = {
  matcher: ["/((?!_next|api|.*\\..*).*)"],
};
