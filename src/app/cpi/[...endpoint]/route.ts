import { NextRequest, NextResponse } from 'next/server';
import { with<PERSON>og<PERSON> } from '@/lib/withLogger';
import { BLOG_API_URL, MAIN_API_URL } from '@/utils/env';

const handleSwitchEndpoint = (typeRequest: string) => {
  switch (typeRequest) {
    case 'home':
      return MAIN_API_URL;
    case 'blog':
    default :
      return BLOG_API_URL;
  }
};

const buildApiUrl = (request: NextRequest) => {
  const urlObj = new URL(request.url);
  const type = request.headers.get('typeEndpoint') ?? 'blog';
  const endpoint = urlObj.pathname.replace(/^\/cpi\//, '').replace(/^\/+/, '');
  const base = handleSwitchEndpoint(type);

  const apiUrl = new URL(`${base}/${endpoint}`);
  urlObj.searchParams.forEach((value, key) => {
    apiUrl.searchParams.append(key, value);
  });

  return apiUrl.toString();
};

const proxyFetch = async (request: NextRequest, method: 'GET' | 'POST') => {
  try {
    const apiUrl = buildApiUrl(request);

    const headers = new Headers();
    request.headers.forEach((value, key) => {
      headers.set(key, value);
    });

    // Uncomment this if you want to pass the token from cookies
    // note: cookie must set in server side by nextjs

    // const token = request.cookies.get('token')?.value;
    // if (token) {
    //   headers.set('Authorization', `Bearer ${token}`);
    // }

    let options: RequestInit = {
      method,
      headers,
      ...( method === 'POST'
        ? { body: request.body, duplex: 'half' as const }
        : {} )
    };

    if (method === 'POST') {
      const body = await request.json();
      options.body = JSON.stringify(body);
    }

    const response = await fetch(apiUrl, {
      ...options,
      ...( method === 'GET' ? { next: { revalidate: 60 } } : {} ),
      credentials: 'include'
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ( {} ));
      return NextResponse.json({ error: errorData || 'Error from API' }, { status: response.status });
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error: any) {
    console.error('Proxy Fetch Error:', error);
    return NextResponse.json({ error: 'Internal server error', message: error?.message }, { status: 500 });
  }
};

export const GET = withLogger((request: NextRequest) => proxyFetch(request, 'GET'));
export const POST = withLogger((request: NextRequest) => proxyFetch(request, 'POST'));
