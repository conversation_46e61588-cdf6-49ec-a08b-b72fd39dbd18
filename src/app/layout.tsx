import React from "react";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { headers } from 'next/headers';
import "animate.css";
import "../../styles/bootstrap.min.css";
import "../../styles/boxicons.min.css";
import "../../styles/flaticon.css";
import "react-accessible-accordion/dist/fancy-example.css";
import "swiper/css";
import "swiper/css/bundle";

// Global Style
import "../../styles/style.css";
// custom style
import "../../styles/custom.css";

import type { Metadata } from "next";
import AosAnimation from "@/components/Layout/AosAnimation";
import GoTop from "@/components/Layout/GoTop";
import { assets } from "@/utils/helper";
import { APP_ENV, CLOUDFLARE_ANALYTICS_TOKEN } from "@/utils/env";
import { CLARITY_PROJECT_ID, ENV } from "@/constants/app";
import { defaultLocale } from "@/i18n";

export const metadata: Metadata = {
  title: "Home - CSlant | Open Source Developer Team",
  description: "CSlant is an all-in-one platform that provides a comprehensive solution to build, manage, and scale your business. Our team of developers, designers, and makers loves sharing knowledge and helping each other grow.",
  icons: {
    icon: [
      { url: assets('favicon.ico'), type: "image/x-icon" },
    ],
    shortcut: assets('favicon.ico'),
    apple: assets('favicon.ico'),
  },
};

type Props = {
  children: React.ReactNode;
};

export default async function RootLayout({ children }: Props) {
  // Get locale from headers set by middleware
  const headersList = await headers();
  const locale = headersList.get('x-locale') || defaultLocale;
  const messages = await getMessages();

  return (
    <html lang={locale}>
      <body>
        <NextIntlClientProvider messages={messages}>
          {children}

          <AosAnimation />

          <GoTop />

          {APP_ENV === ENV.PROD && (
            <>
              <script
                defer
                src="https://static.cloudflareinsights.com/beacon.min.js"
                data-cf-beacon={`{"token": "${CLOUDFLARE_ANALYTICS_TOKEN}"}`}
              ></script>
              <script
                dangerouslySetInnerHTML={{
                  __html: `
                  (function(c,l,a,r,i,t,y){
                      c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                      t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                      y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
                  })(window, document, "clarity", "script", "${CLARITY_PROJECT_ID}");
                `,
                }}
              />
            </>
          )}
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
