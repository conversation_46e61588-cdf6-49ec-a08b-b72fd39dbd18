import Navbar from "../../../components/Layout/Navbar";
import Footer from "../../../components/Layout/Footer";
import PageBanner from "../../../components/Common/PageBanner";
import BlogDetailsContent from "../../../components/Blog/BlogDetailsContent";
import { blogPostApi } from '@/api/blog/post';

const handleFetchPostDetail =async ()=>{
  return await blogPostApi.get({
    slug:'https://jsonplaceholder.typicode.com/posts/1',
  })
}

export default async function Page() {
  await handleFetchPostDetail();

  return (
    <>
      <Navbar />

      <PageBanner pageTitle="Blog Details" />

      <BlogDetailsContent />

      <Footer />
    </>
  );
};
