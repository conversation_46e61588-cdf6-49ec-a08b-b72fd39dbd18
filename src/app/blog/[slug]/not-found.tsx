import Link from 'next/link';
import Navbar from "../../../components/Layout/Navbar";
import Footer from "../../../components/Layout/Footer";
import PageBanner from "../../../components/Common/PageBanner";

export default function BlogNotFound() {
  return (
    <>
      <Navbar />
      
      <PageBanner pageTitle="Blog Post Not Found" />
      
      <div className="error-area ptb-80">
        <div className="container">
          <div className="row">
            <div className="col-lg-8 col-md-12 mx-auto text-center">
              <div className="error-content">
                <h1>404</h1>
                <h2>Blog Post Not Found</h2>
                <p>
                  Sorry, the blog post you are looking for could not be found. 
                  It may have been moved, deleted, or the URL might be incorrect.
                </p>
                
                <div className="error-actions">
                  <Link href="/blog" className="btn btn-primary">
                    <i className="flaticon-left-arrow"></i>
                    Back to Blog
                  </Link>
                  
                  <Link href="/" className="btn btn-secondary ml-3">
                    <i className="flaticon-home"></i>
                    Go to Homepage
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <Footer />
    </>
  );
}
