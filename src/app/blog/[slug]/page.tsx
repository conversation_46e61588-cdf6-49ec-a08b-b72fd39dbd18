import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Navbar from "../../../components/Layout/Navbar";
import Footer from "../../../components/Layout/Footer";
import PageBanner from "../../../components/Common/PageBanner";
import BlogDetailsContent from "../../../components/Blog/BlogDetailsContent";
import { BlogService } from '@/services/apis/blog';
import { TDetailLoader } from '@/types/blog';

interface BlogDetailPageProps {
  params: {
    slug: string;
  };
}

// Generate metadata for SEO
export async function generateMetadata({ params }: BlogDetailPageProps): Promise<{ title: string; description: string; keywords: string | undefined; authors: { name: string | undefined }[]; openGraph: { title: string; description: string; images: (string | undefined)[]; type: string; url: string | undefined; publishedTime: string | undefined; modifiedTime: string | undefined }; twitter: { card: string; title: string; description: string; images: (string | undefined)[] } }> {
  const { slug } = params;

  const response = await BlogService.getPostBySlug(slug);

  if (!response.success || !response.data) {
    return {
      title: 'Blog Post Not Found',
      description: 'The requested blog post could not be found.'
    };
  }

  const post = response.data;
  const metaSEO = BlogService.generateMetaSEO(post);

  return {
    title: metaSEO.title,
    description: metaSEO.description,
    keywords: metaSEO.keywords,
    authors: [{ name: metaSEO.author }],
    openGraph: {
      title: metaSEO.title,
      description: metaSEO.description,
      images: [metaSEO.image],
      type: 'article',
      url: metaSEO.url,
      publishedTime: metaSEO.publishedTime,
      modifiedTime: metaSEO.modifiedTime,
    },
    twitter: {
      card: 'summary_large_image',
      title: metaSEO.title,
      description: metaSEO.description,
      images: [metaSEO.image],
    }
  };
}

// Fetch blog post data
async function getBlogPostData(slug: string): Promise<TDetailLoader & { relatedPosts: any[] }> {
  const response = await BlogService.getPostBySlug(slug);

  if (!response.success || !response.data) {
    console.error('Failed to fetch blog post:', response.error);
    notFound();
  }

  const post = response.data;
  const metaSEO = BlogService.generateMetaSEO(post);

  // Fetch related posts - không bắt buộc phải có
  const relatedResponse = await BlogService.getRelatedPosts(post.id, 3);
  const relatedPosts = relatedResponse.success && relatedResponse.data ? relatedResponse.data : [];

  return {
    blogPost: {
      data: post,
      error: false,
      message: null
    },
    meta: metaSEO,
    relatedPosts: relatedPosts
  };
}

export default async function BlogDetailPage({ params }: BlogDetailPageProps) {
  const { slug } = params;

  // Fetch blog post data
  const blogData = await getBlogPostData(slug);

  return (
    <>
      <Navbar />

      <PageBanner pageTitle={blogData.blogPost.data.name} />

      <BlogDetailsContent
        blogPost={blogData.blogPost.data}
        meta={blogData.meta}
        relatedPosts={blogData.relatedPosts}
      />

      <Footer />
    </>
  );
}

// Optional: Generate static params for static generation
export async function generateStaticParams() {
  // Có thể fetch danh sách các slug để pre-generate
  // Tạm thời return empty array để sử dụng dynamic rendering
  return [];
}
