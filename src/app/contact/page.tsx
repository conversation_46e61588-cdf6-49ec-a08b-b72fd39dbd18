import React from "react";
import { useTranslations } from 'next-intl';
import Navbar from "../../components/Layout/Navbar";
import Footer from "../../components/Layout/Footer";
import PageBanner from "../../components/Common/PageBanner";
import ContactInfo from "../../components/Contact/ContactInfo";
import GoogleMap from "../../components/Contact/GoogleMap";
import ContactForm from "../../components/Contact/ContactForm";

export default function Page() {
  const t = useTranslations('pages.contact');

  return (
    <>
      <Navbar />

      <PageBanner pageTitle={t('title')} />

      <ContactInfo />

      <GoogleMap />

      <ContactForm />

      <Footer />
    </>
  );
};
