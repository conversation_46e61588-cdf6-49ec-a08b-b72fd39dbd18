import React from "react";
import Navbar from '../../../components/Layout/Navbar';
import Footer from "../../../components/Layout/Footer";
import { useTranslations } from 'next-intl';

export default function Blog() {
  const t = useTranslations('pages.blog');

  return (
    <>
      <Navbar />

      <div className="page-title-area">
        <div className="container">
          <div className="page-title-content">
            <h1>{t('title')}</h1>
            <p>{t('subtitle')}</p>
          </div>
        </div>
      </div>

      <div className="blog-area ptb-80">
        <div className="container">
          <div className="row">
            <div className="col-lg-12">
              <div className="blog-content">
                <h2>{t('heading')}</h2>
                <p>{t('description')}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </>
  );
}
