import React from "react";
import Navbar from '../../../components/Layout/Navbar';
import Footer from "../../../components/Layout/Footer";
import { useTranslations } from 'next-intl';

export default function About() {
  const t = useTranslations('pages.about');

  return (
    <>
      <Navbar />

      <div className="page-title-area">
        <div className="container">
          <div className="page-title-content">
            <h1>{t('title')}</h1>
            <p>{t('subtitle')}</p>
          </div>
        </div>
      </div>

      <div className="about-area ptb-80">
        <div className="container">
          <div className="row align-items-center">
            <div className="col-lg-12">
              <div className="about-content">
                <h2>{t('welcome')}</h2>
                <p>{t('description')}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </>
  );
}
