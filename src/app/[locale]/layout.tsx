import React from "react";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { locales } from '@/i18n';
import "animate.css";
import "../../../styles/bootstrap.min.css";
import "../../../styles/boxicons.min.css";
import "../../../styles/flaticon.css";
import "react-accessible-accordion/dist/fancy-example.css";
import "swiper/css";
import "swiper/css/bundle";

// Global Style
import "../../../styles/style.css";
// Global Responsive Style (Imported in custom.css)
// import "../../../styles/responsive.css";

// custom style
import "../../../styles/custom.css";

// Multicolor if you want this just one color comment out
// import '../../../styles/colors/brink-pink-style.css'
// import '../../../styles/colors/pink-style.css'
// import '../../../styles/colors/purple-style.css'

import type { Metadata } from "next";
import { Poppins } from "next/font/google";
import AosAnimation from "@/components/Layout/AosAnimation";
import GoTop from "@/components/Layout/GoTop";
import { assets } from "@/utils/helper";
import { APP_ENV, CLOUDFLARE_ANALYTICS_TOKEN } from "@/utils/env";
import { CLARITY_PROJECT_ID, ENV } from "@/constants/app";

const poppins = Poppins({
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
  subsets: ["latin"],
});

type Props = {
  children: React.ReactNode;
  params: { locale: string };
};

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export async function generateMetadata({ params: { locale } }: Props): Promise<Metadata> {
  const messages = await getMessages();

  return {
    title: (messages as any).home?.title || "Home - CSlant | Open Source Developer Team",
    description: (messages as any).home?.description || "CSlant is an all-in-one platform that provides a comprehensive solution to build, manage, and scale your business. Our team of developers, designers, and makers loves sharing knowledge and helping each other grow.",
    icons: {
      icon: [
        { url: assets('favicon.ico'), type: "image/x-icon" },
      ],
      shortcut: assets('favicon.ico'),
      apple: assets('favicon.ico'),
    },
  };
}

export default async function LocaleLayout({
  children,
  params: { locale }
}: Props) {
  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as any)) {
    notFound();
  }

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages();

  return (
    <html lang={locale}>
      <body className={poppins.className}>
        <NextIntlClientProvider messages={messages}>
          {children}

          <AosAnimation />

          <GoTop />

          {APP_ENV === ENV.PROD && (
            <>
              <script
                defer
                src="https://static.cloudflareinsights.com/beacon.min.js"
                data-cf-beacon={`{"token": "${CLOUDFLARE_ANALYTICS_TOKEN}"}`}
              ></script>
              <script
                dangerouslySetInnerHTML={{
                  __html: `
                  (function(c,l,a,r,i,t,y){
                      c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                      t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                      y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
                  })(window, document, "clarity", "script", "${CLARITY_PROJECT_ID}");
                `,
                }}
              />
            </>
          )}
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
