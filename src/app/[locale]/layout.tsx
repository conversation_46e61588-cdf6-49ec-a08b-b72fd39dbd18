import React from "react";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { locales } from '@/i18n';
import type { Metadata } from "next";
import { assets } from "@/utils/helper";
import { notFound } from "next/navigation";

type Props = {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
};

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { locale } = await params;
  const messages = await getMessages();

  return {
    title: (messages as any).home?.title || "Home - CSlant | Open Source Developer Team",
    description: (messages as any).home?.description || "CSlant is an all-in-one platform that provides a comprehensive solution to build, manage, and scale your business. Our team of developers, designers, and makers loves sharing knowledge and helping each other grow.",
    icons: {
      icon: [
        { url: assets('favicon.ico'), type: "image/x-icon" },
      ],
      shortcut: assets('favicon.ico'),
      apple: assets('favicon.ico'),
    },
  };
}

export default async function LocaleLayout({
  children,
  params
}: Props) {
  const { locale } = await params;

  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as any)) {
    notFound();
  }

  // Providing all messages to the client side
  const messages = await getMessages();

  return (
    <NextIntlClientProvider messages={messages}>
      {children}
    </NextIntlClientProvider>
  );
}
