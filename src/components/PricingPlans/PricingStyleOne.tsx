"use client";

import React from "react";
import Link from "next/link";
import * as Icon from "react-feather";
import { pricingPlansData } from "@/api/package";
import EffectWrapper from "../EffectWrapper";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay, Navigation } from "swiper/modules";
import Image from "next/image";
import { assets } from "@/utils/helper";

const PricingStyleOne: React.FC = () => {
  return (
    <>
      <div className="pricing-area pt-80 pb-50 bg-f9f6f6">
        <div className="container">
          <div className="section-title">
            <h2>Pricing Plans</h2>
            <div className="bar"></div>
            <p>
              Flexible pricing plans tailored to your needs and budget.<br />
              Choose the perfect package to grow your business with confidence.
            </p>
          </div>

          <div className="row position-relative package-slider">
            <div className="custom-swiper-button-prev">
              <Icon.ChevronLeft />
            </div>
            <div className="custom-swiper-button-next">
              <Icon.ChevronRight />
            </div>

            <Swiper
              spaceBetween={30}
              pagination={{
                clickable: true,
              }}
              navigation={{
                prevEl: '.custom-swiper-button-prev',
                nextEl: '.custom-swiper-button-next',
              }}
              autoplay={{
                delay: 6000,
                pauseOnMouseEnter: true,
              }}
              breakpoints={{
                0: {
                  slidesPerView: 1,
                },
                576: {
                  slidesPerView: 1,
                },
                768: {
                  slidesPerView: 2,
                },
                992: {
                  slidesPerView: 3,
                },
              }}
              modules={[Pagination, Autoplay, Navigation]}
              className="pricing-slides"
            >
              {pricingPlansData.map((plan) => (
                <SwiperSlide key={plan.id}>
                  <EffectWrapper delayTime={plan.delay / 1000}>
                    <div className={`modern-pricing-table pricing-table ${plan.isActive ? 'active-plan' : ''}`}>
                      <div className="pricing-header">
                        <h3>{plan.title}</h3>
                      </div>

                      <div className="pricing-content">
                        <div className="price">
                          <span>
                            <sup>$</sup>
                            {plan.price}
                            <span className="duration">{plan.duration}</span>
                          </span>
                        </div>

                        <div className="pricing-features">
                          <ul>
                            {plan.features.map((feature, idx) => (
                              <li key={idx} className={feature.isActive ? "active" : ""}>
                                {/*{feature.isActive ? (*/}
                                {/*  <Icon.Check color="#4CAF50" />*/}
                                {/*) : (*/}
                                {/*  <Icon.X color="#e50000" />*/}
                                {/*)}*/}
                                {feature.feature}
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div className="pricing-footer">
                          <Link href="#" className={plan.isActive ? "btn btn-primary" : "btn btn-secondary"}>
                            Order Now
                          </Link>
                        </div>
                      </div>
                    </div>
                  </EffectWrapper>
                </SwiperSlide>
              ))}
            </Swiper>
          </div>
        </div>
        {/* Shape Images */}
        <div className="shape8 rotateme">
          <Image src={assets('v2/images/shape2.svg')} alt="shape" width={22} height={22} />
        </div>
        <div className="shape2 rotateme">
          <Image src={assets('v2/images/shape2.svg')} alt="shape" width={22} height={22} />
        </div>
        <div className="shape7">
          <Image src={assets('v2/images/shape4.svg')} alt="shape" width={21} height={20} />
        </div>
        <div className="shape4">
          <Image src={assets('v2/images/shape4.svg')} alt="shape" width={21} height={20} />
        </div>
      </div>
    </>
  );
};

export default PricingStyleOne;
