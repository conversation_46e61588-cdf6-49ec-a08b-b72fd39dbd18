"use client";

import React, { useState, useRef, useEffect } from 'react';
import { useLocale, useTranslations } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import * as Icon from "react-feather";

const LanguageSwitcher: React.FC = () => {
  const t = useTranslations('common');
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const languages = [
    { code: 'en', name: t('english'), flag: '🇺🇸' },
    { code: 'vi', name: t('vietnamese'), flag: '🇻🇳' }
  ];

  const currentLanguage = languages.find(lang => lang.code === locale) || languages[0];

  const handleLanguageChange = (newLocale: string) => {
    // Set cookie for locale preference
    document.cookie = `cslant_locale=${newLocale}; path=/; max-age=31536000`; // 1 year

    // Check if current path has locale prefix (e.g., /en/about, /vi/contact)
    const hasLocalePrefix = pathname.startsWith('/en/') || pathname.startsWith('/vi/') ||
                           pathname === '/en' || pathname === '/vi';

    if (hasLocalePrefix) {
      // Replace current locale with new one (e.g., /en/about -> /vi/about)
      const pathWithoutLocale = pathname.replace(/^\/(en|vi)/, '') || '/';
      router.push(`/${newLocale}${pathWithoutLocale}`);
    } else {
      // For paths without locale prefix (e.g., /about), just reload to apply new locale
      window.location.reload();
    }

    setIsOpen(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="language-switcher" ref={dropdownRef}>
      <button
        className="language-btn"
        onClick={() => setIsOpen(!isOpen)}
        aria-label={t('language')}
      >
        <span className="flag">{currentLanguage.flag}</span>
        <span className="lang-code">{currentLanguage.code.toUpperCase()}</span>
        <Icon.ChevronDown size={16} />
      </button>
      
      {isOpen && (
        <div className="language-dropdown">
          {languages.map((language) => (
            <button
              key={language.code}
              className={`language-option ${locale === language.code ? 'active' : ''}`}
              onClick={() => handleLanguageChange(language.code)}
            >
              <span className="flag">{language.flag}</span>
              <span className="name">{language.name}</span>
            </button>
          ))}
        </div>
      )}

      <style jsx>{`
        .language-switcher {
          position: relative;
          display: inline-block;
        }

        .language-btn {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 8px 12px;
          background: transparent;
          border: 1px solid #e1e5e9;
          border-radius: 6px;
          cursor: pointer;
          font-size: 14px;
          color: #333;
          transition: all 0.3s ease;
        }

        .language-btn:hover {
          background: #f8f9fa;
          border-color: #007bff;
        }

        .flag {
          font-size: 16px;
        }

        .lang-code {
          font-weight: 500;
          min-width: 20px;
        }

        .language-dropdown {
          position: absolute;
          top: 100%;
          right: 0;
          background: white;
          border: 1px solid #e1e5e9;
          border-radius: 6px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          z-index: 1000;
          min-width: 140px;
          margin-top: 4px;
        }

        .language-option {
          display: flex;
          align-items: center;
          gap: 8px;
          width: 100%;
          padding: 10px 12px;
          background: none;
          border: none;
          cursor: pointer;
          font-size: 14px;
          color: #333;
          transition: background-color 0.2s ease;
        }

        .language-option:hover {
          background: #f8f9fa;
        }

        .language-option.active {
          background: #007bff;
          color: white;
        }

        .language-option .name {
          flex: 1;
          text-align: left;
        }
      `}</style>
    </div>
  );
};

export default LanguageSwitcher;
