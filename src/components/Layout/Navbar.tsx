"use client";

import React, { useState, useEffect, useRef } from 'react';
import * as Icon from "react-feather";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useTranslations, useLocale } from 'next-intl';
import { LOGO } from '@/constants/asset';
import { assets } from '@/utils/helper';
import LanguageSwitcher from './LanguageSwitcher';


const Navbar: React.FC = () => {
  const currentRoute = usePathname();
  const t = useTranslations('navigation');
  const locale = useLocale();

  const [menu, setMenu] = useState<boolean>(true);
  const isFirstRender = useRef(true);
  const [sticky, setSticky] = useState(true);

  const toggleNavbar = () => {
    setMenu(!menu);
  };

  const handleStickyNavbar = () => {
    if (window.scrollY >= 80) {
      setSticky(true);
    } else {
      setSticky(false);
    }
  };

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      handleStickyNavbar();
    }
    window.addEventListener('scroll', handleStickyNavbar);
    window.addEventListener('load', handleStickyNavbar);
    return () => {
      window.removeEventListener('scroll', handleStickyNavbar);
      window.removeEventListener('load', handleStickyNavbar);
    };
  }, []);

  const classOne = menu
    ? "collapse navbar-collapse"
    : "collapse navbar-collapse show";
  const classTwo = menu
    ? "navbar-toggler navbar-toggler-right collapsed"
    : "navbar-toggler navbar-toggler-right";

  return (
    <>
      <header id="header" className={`headroom ${sticky ? 'is-sticky' : ''}`}>
        <div className="startp-nav">
          <div className="container">
            <nav className="navbar navbar-expand-md navbar-light">
              <Link href="/" className="navbar-brand" >
                <Image src={assets(LOGO)} alt="logo" width={110} height={36} />
              </Link>

              <button
                onClick={toggleNavbar}
                className={classTwo}
                type="button"
                data-toggle="collapse"
                data-target="#navbarSupportedContent"
                aria-controls="navbarSupportedContent"
                aria-expanded="false"
                aria-label="Toggle navigation"
              >
                <span className="icon-bar top-bar"></span>
                <span className="icon-bar middle-bar"></span>
                <span className="icon-bar bottom-bar"></span>
              </button>

              <div className={classOne} id="navbarSupportedContent">
                <ul className="navbar-nav ms-auto">
                  <li className="nav-item">
                    <Link
                      href="#"
                      onClick={(e) => e.preventDefault()}
                      className="nav-link"
                    >
                      {t('about')} <Icon.ChevronDown />
                    </Link>

                    <ul className="dropdown-menu">
                      <li className="nav-item">
                        <Link
                          href="/about"
                          onClick={toggleNavbar}
                          className={`nav-link ${
                            currentRoute == "/about" && "active"
                          }`}
                        >
                          {t('about')}
                        </Link>
                      </li>

                      <li className="nav-item">
                        <Link
                          href="/terms-conditions"
                          onClick={toggleNavbar}
                          className={`nav-link ${
                            currentRoute == "/terms-conditions" && "active"
                          }`}

                        >
                          Terms & Conditions
                        </Link>
                      </li>

                      <li className="nav-item">
                        <Link
                          href="/privacy-policy"
                          onClick={toggleNavbar}
                          className={`nav-link ${
                            currentRoute == "/privacy-policy" && "active"
                          }`}

                        >
                          Privacy Policy
                        </Link>
                      </li>

                      <li className="nav-item">
                        <Link
                          href="/cookie-policy"
                          onClick={toggleNavbar}
                          className={`nav-link ${
                            currentRoute == "/cookie-policy" && "active"
                          }`}

                        >
                          Cookie Policy
                        </Link>
                      </li>

                      <li className="nav-item">
                        <Link
                          href="/faq"
                          onClick={toggleNavbar}
                          className={`nav-link ${
                            currentRoute == "/faq" && "active"
                          }`}

                        >
                          {t('faq')}
                        </Link>
                      </li>
                    </ul>
                  </li>

                  <li className="nav-item">
                    <Link
                      href="https://docs.cslant.com"
                      target="_blank"
                      className="nav-link"
                    >
                      {t('docs')} <Icon.ChevronDown />
                    </Link>

                    <ul className="dropdown-menu">
                      <li className="nav-item">
                        <Link
                          href="https://docs.cslant.com/telegram-git-notifier"
                          onClick={toggleNavbar}
                          className="nav-link"
                          target="_blank"
                        >
                          Telegram Git Notifier
                        </Link>
                      </li>

                      <li className="nav-item">
                        <Link
                          href="https://docs.cslant.com/laravel-like"
                          onClick={toggleNavbar}
                          className="nav-link"
                          target="_blank"
                        >
                          Laravel Like
                        </Link>
                      </li>
                    </ul>
                  </li>

                  <li className="nav-item">
                    <Link
                      href="/blog"
                      onClick={toggleNavbar}
                      className="nav-link"
                    >
                      {t('blog')}
                    </Link>
                  </li>

                  <li className="nav-item">
                    <Link
                      href="/contact"
                      onClick={toggleNavbar}
                      className={`nav-link ${
                        currentRoute == "/contact" && "active"
                      }`}
                    >
                      {t('contact')}
                    </Link>
                  </li>
                </ul>
              </div>

              {/* Others option */}
              <div className="others-option">
                {/*<Link href="/cart/" className="cart-wrapper-btn" >*/}
                {/*  <Icon.ShoppingCart />*/}
                {/*  <span>3</span>*/}
                {/*</Link>*/}

                <LanguageSwitcher />

                <Link href="/contact" className="btn btn-light" >
                  {t('support')}
                </Link>
              </div>
            </nav>
          </div>
        </div>
      </header>
    </>
  );
};

export default Navbar;
