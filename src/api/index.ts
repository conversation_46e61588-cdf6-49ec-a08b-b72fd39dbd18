import { FRONT_END_DOMAIN, ROUTE_API_HANDLER } from '@/utils/env';

export type FetchOptions<T = any> = {
  endpoint: string;
  type?: string;
  params?: Record<string, any>;
  headers?: HeadersInit;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  body?: BodyInit | null;
  next?: { revalidate: number };
};

function buildUrlWithParams(url: string, params?: Record<string, any>): string {
  if (!params) return url;
  const query = new URLSearchParams();
  for (const key in params) {
    if (params[key] !== undefined && params[key] !== null) {
      query.append(key, String(params[key]));
    }
  }
  return url.includes('?') ? `${url}&${query}` : `${url}?${query}`;
}

export async function fetcher<T = any>({
  endpoint,
  type = 'blog',
  params,
  headers = {},
  method = 'GET',
  body = null,
  next,
}: FetchOptions): Promise<{ data: T | null; error: any; meta: any }> {
  const url = buildUrlWithParams(`${FRONT_END_DOMAIN}/${ROUTE_API_HANDLER}/${endpoint}`, params);

  try {
    const normalizedHeaders: HeadersInit = {
      'Content-Type': 'application/json',
      ...(headers instanceof Headers ? Object.fromEntries(headers.entries()) : headers),
      typeEndpoint: type ?? 'blog'
    };

    const res = await fetch(url, {
      method,
      headers: normalizedHeaders,
      body,
      ...(next ? { next } : {}),
    });


    const json = await res.json();
    if (!res.ok) {

      return { data: null, error: { message: await res.text() }, meta: null };
    }

    return { data: json?.data ?? null, meta: json?.meta ?? null, error: null };
  } catch (error) {
    return { data: null, error, meta: null };
  }
}
