import { FRONT_END_DOMAIN, ROUTE_API_HANDLER } from '@/utils/env';
import { getLocale } from 'next-intl/server';
import { headers } from 'next/headers';

export type FetchOptions<T = any> = {
  endpoint: string;
  type?: string;
  params?: Record<string, any>;
  headers?: HeadersInit;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  body?: BodyInit | null;
  next?: { revalidate: number };
  locale?: string; // Add locale option
};

function buildUrlWithParams(url: string, params?: Record<string, any>): string {
  if (!params) return url;
  const query = new URLSearchParams();
  for (const key in params) {
    if (params[key] !== undefined && params[key] !== null) {
      query.append(key, String(params[key]));
    }
  }
  return url.includes('?') ? `${url}&${query}` : `${url}?${query}`;
}

async function getLocaleFromContext(): Promise<string> {
  try {
    // Try to get locale from next-intl (server-side)
    const locale = await getLocale();
    return locale;
  } catch {
    try {
      // Fallback: get from headers (set by middleware)
      const headersList = await headers();
      return headersList.get('x-locale') || 'en';
    } catch {
      // Final fallback
      return 'en';
    }
  }
}

export async function fetcher<T = any>({
  endpoint,
  type = 'blog',
  params,
  headers = {},
  method = 'GET',
  body = null,
  next,
  locale,
}: FetchOptions): Promise<{ data: T | null; error: any; meta: any }> {
  // Get locale if not provided
  const currentLocale = locale || await getLocaleFromContext();

  // Add language parameter to params
  const paramsWithLang = {
    ...params,
    lang: currentLocale,
  };

  const url = buildUrlWithParams(`${FRONT_END_DOMAIN}/${ROUTE_API_HANDLER}/${endpoint}`, paramsWithLang);

  try {
    const normalizedHeaders: HeadersInit = {
      'Content-Type': 'application/json',
      ...(customHeaders instanceof Headers ? Object.fromEntries(customHeaders.entries()) : customHeaders),
      typeEndpoint: type ?? 'blog',
      'Accept-Language': currentLocale, // Add Accept-Language header
    };

    const res = await fetch(url, {
      method,
      headers: normalizedHeaders,
      body,
      ...(next ? { next } : {}),
    });


    const json = await res.json();
    if (!res.ok) {

      return { data: null, error: { message: await res.text() }, meta: null };
    }

    return { data: json?.data ?? null, meta: json?.meta ?? null, error: null };
  } catch (error) {
    return { data: null, error, meta: null };
  }
}
