import { fetcher } from '@/api';

export const blogPostApi = {
  get: async <T = any>({
    params,
    slug = '',
    locale, // Add locale parameter
  }: {
    params?: Record<string, string | number | string[] | number[] | undefined>;
    slug?: string;
    type?: string;
    locale?: string; // Add locale parameter
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const path = slug ? `posts/${slug}` : 'posts';
    return fetcher<T>({
      endpoint: path,
      params,
      type: 'blog',
      locale // Pass locale to fetcher
    });
  }
};
