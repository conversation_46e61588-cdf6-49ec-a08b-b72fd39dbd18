import { FRONT_END_DOMAIN, ROUTE_API_HANDLER } from '@/utils/env';
import { getLocale } from 'next-intl/server';
import { headers } from 'next/headers';

export type ServerFetchOptions<T = any> = {
  endpoint: string;
  type?: string;
  params?: Record<string, any>;
  headers?: HeadersInit;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  body?: BodyInit | null;
  next?: { revalidate: number };
  locale?: string;
};

function buildUrlWithParams(url: string, params?: Record<string, any>): string {
  if (!params) return url;
  const query = new URLSearchParams();
  for (const key in params) {
    if (params[key] !== undefined && params[key] !== null) {
      query.append(key, String(params[key]));
    }
  }
  return url.includes('?') ? `${url}&${query}` : `${url}?${query}`;
}

async function getServerLocale(): Promise<string> {
  try {
    // Try to get locale from next-intl (server-side)
    const locale = await getLocale();
    return locale;
  } catch {
    try {
      // Fallback: get from headers (set by middleware)
      const headersList = await headers();
      return headersList.get('x-locale') || 'en';
    } catch {
      // Final fallback
      return 'en';
    }
  }
}

/**
 * Server-only fetcher function that can access next/headers
 * Use this in Server Components
 */
export async function serverFetcher<T = any>({
  endpoint,
  type = 'blog',
  params = {},
  headers: customHeaders = {},
  method = 'GET',
  body = null,
  next,
  locale,
}: ServerFetchOptions): Promise<{ data: T | null; error: any; meta: any }> {
  // Get locale if not provided
  const currentLocale = locale || await getServerLocale();
  
  // Add language parameter to params
  const paramsWithLang = {
    ...params,
    lang: currentLocale,
  };

  const url = buildUrlWithParams(`${FRONT_END_DOMAIN}/${ROUTE_API_HANDLER}/${endpoint}`, paramsWithLang);

  try {
    const normalizedHeaders: HeadersInit = {
      'Content-Type': 'application/json',
      ...(customHeaders instanceof Headers ? Object.fromEntries(customHeaders.entries()) : customHeaders),
      typeEndpoint: type ?? 'blog',
      'Accept-Language': currentLocale,
    };

    const res = await fetch(url, {
      method,
      headers: normalizedHeaders,
      body,
      ...(next ? { next } : {}),
    });

    const json = await res.json();
    if (!res.ok) {
      return { data: null, error: { message: await res.text() }, meta: null };
    }

    return { data: json?.data ?? null, meta: json?.meta ?? null, error: null };
  } catch (error) {
    return { data: null, error, meta: null };
  }
}
