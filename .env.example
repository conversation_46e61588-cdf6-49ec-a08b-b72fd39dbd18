##
# ENVIRONMENT
## ------------
# Example: local, dev, staging, production
NEXT_PUBLIC_APP_ENV=local

##
# APP CONFIG
## -------------
BASE_ASSET_PREFIX=
NEXT_PUBLIC_DIST_DIR="dist"

NEXT_PUBLIC_APP_URL=http://${CSLANT_DOMAIN}
NEXT_PUBLIC_APP_NAME="CSlant"

NEXT_PUBLIC_BASE_DOMAIN=${CSLANT_DOMAIN}

NEXT_PUBLIC_CLOUDFLARE_ANALYTICS_TOKEN=xxxxxxxxxxxxxxxxxxxx

##
# URL - API CONFIG
## -------------
# Set to `true` if the API is served over HTTPS. Set to `false` if the API is served over HTTP.
NEXT_PUBLIC_IS_USE_HTTPS=${IS_SSL}
NEXT_PUBLIC_HTTP_PORT=${NGINX_HOST_HTTP_PORT}
NEXT_PUBLIC_HTTPS_PORT=${NGINX_HOST_HTTPS_PORT}

NEXT_PUBLIC_MAIN_API_DOMAIN=${API_DOMAIN}

# API Configuration - Toggle between direct API calls or Next.js API proxy
# Set to 'true' to use Next.js API proxy (/cpi), 'false' for direct API calls
NEXT_PUBLIC_USE_API_PROXY=false

# Direct API URLs (used when NEXT_PUBLIC_USE_API_PROXY=false)
NEXT_PUBLIC_BLOG_API_URL=https://api.cslant.com/api
NEXT_PUBLIC_MAIN_API_URL=https://main-api.cslant.com/api

# Legacy API URL (used when NEXT_PUBLIC_USE_API_PROXY=true)
NEXT_PUBLIC_BLOG_API_URL_LEGACY=${COMPOSE_PROJECT_NAME}-nginx/${BLOG_API_ROUTE_PREFIX}

##
# ASSET CONFIG
## -------------
NEXT_PUBLIC_ASSETS_DOMAIN=${ASSETS_DOMAIN}
NEXT_PUBLIC_BLOG_ASSETS_PATH=/blog

##
# LOGGING
## -------------
# set to `true` if you want to enable logger, otherwise set to `false`
NEXT_ENABLE_LOGGER=true

##
# NEXT api route handler
##
NEXT_PUBLIC_ROUTE_API_HANDLER=cpi

NEXT_PUBLIC_COOKIE_DOMAIN=${CSLANT_DOMAIN}
