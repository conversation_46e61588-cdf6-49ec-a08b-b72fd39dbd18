# API Internationalization Guide - Next.js Frontend + Laravel Backend

## 📋 Overview

This guide covers how to implement API internationalization by passing language parameters from Next.js frontend to Laravel backend for all API calls.

## 🎯 Architecture

```
Next.js Frontend (next-intl) → API Calls with lang parameter → Laravel Backend (Localization)
```

## 🔧 Frontend Implementation (Next.js)

### Step 1: Create API Client with Language Support

Create a centralized API client that automatically includes language parameter:

```typescript
// src/lib/api-client.ts
import { getLocale } from 'next-intl/server';
import { headers } from 'next/headers';

interface ApiConfig {
  baseURL: string;
  timeout?: number;
}

class ApiClient {
  private baseURL: string;
  private timeout: number;

  constructor(config: ApiConfig) {
    this.baseURL = config.baseURL;
    this.timeout = config.timeout || 10000;
  }

  private async getLanguage(): Promise<string> {
    try {
      // Try to get locale from next-intl
      const locale = await getLocale();
      return locale;
    } catch {
      // Fallback: get from headers (set by middleware)
      const headersList = await headers();
      return headersList.get('x-locale') || 'en';
    }
  }

  private async buildUrl(endpoint: string, params?: Record<string, any>): Promise<string> {
    const lang = await this.getLanguage();
    const url = new URL(endpoint, this.baseURL);
    
    // Add language parameter
    url.searchParams.set('lang', lang);
    
    // Add other parameters
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.set(key, String(value));
        }
      });
    }
    
    return url.toString();
  }

  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const url = await this.buildUrl(endpoint, params);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async post<T>(endpoint: string, data?: any, params?: Record<string, any>): Promise<T> {
    const lang = await this.getLanguage();
    const url = new URL(endpoint, this.baseURL);
    
    // Add language parameter to URL
    url.searchParams.set('lang', lang);
    
    // Add other URL parameters
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.set(key, String(value));
        }
      });
    }

    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: data ? JSON.stringify(data) : undefined,
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async put<T>(endpoint: string, data?: any, params?: Record<string, any>): Promise<T> {
    const lang = await this.getLanguage();
    const url = new URL(endpoint, this.baseURL);
    
    url.searchParams.set('lang', lang);
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.set(key, String(value));
        }
      });
    }

    const response = await fetch(url.toString(), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: data ? JSON.stringify(data) : undefined,
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async delete<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const url = await this.buildUrl(endpoint, params);
    
    const response = await fetch(url, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }
}

// Create singleton instance
export const apiClient = new ApiClient({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api',
  timeout: 10000,
});

export default apiClient;
```

### Step 2: Create Client-Side API Hook

For client-side components, create a hook that uses the current locale:

```typescript
// src/hooks/useApiClient.ts
'use client';

import { useLocale } from 'next-intl';
import { useMemo } from 'react';

interface ApiConfig {
  baseURL: string;
  timeout?: number;
}

class ClientApiClient {
  private baseURL: string;
  private timeout: number;
  private locale: string;

  constructor(config: ApiConfig & { locale: string }) {
    this.baseURL = config.baseURL;
    this.timeout = config.timeout || 10000;
    this.locale = config.locale;
  }

  private buildUrl(endpoint: string, params?: Record<string, any>): string {
    const url = new URL(endpoint, this.baseURL);
    
    // Add language parameter
    url.searchParams.set('lang', this.locale);
    
    // Add other parameters
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.set(key, String(value));
        }
      });
    }
    
    return url.toString();
  }

  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    const url = this.buildUrl(endpoint, params);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  async post<T>(endpoint: string, data?: any, params?: Record<string, any>): Promise<T> {
    const url = new URL(endpoint, this.baseURL);
    
    // Add language parameter to URL
    url.searchParams.set('lang', this.locale);
    
    // Add other URL parameters
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.set(key, String(value));
        }
      });
    }

    const response = await fetch(url.toString(), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: data ? JSON.stringify(data) : undefined,
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // Add other methods (put, delete) similar to above
}

export function useApiClient() {
  const locale = useLocale();
  
  return useMemo(() => new ClientApiClient({
    baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api',
    timeout: 10000,
    locale,
  }), [locale]);
}
```
