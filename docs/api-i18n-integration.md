# API Internationalization Integration Guide

## 📋 Overview

This guide covers how to integrate multi-language support between Next.js frontend and Laravel API backend, ensuring consistent locale handling across the entire application.

## 🏗️ Architecture Overview

```mermaid
graph TD
    A[Next.js Frontend] --> B[Middleware Detects Locale]
    B --> C[Set cslant_locale Cookie]
    C --> D[API Request with Headers]
    D --> E[Laravel API Middleware]
    E --> F[Set App Locale]
    F --> G[Process Request]
    G --> H[Return Localized Response]
    H --> I[Frontend Receives Data]
    
    style A fill:#e1f5fe,stroke:#333,stroke-width:2px,color:#000
    style B fill:#fff3e0,stroke:#333,stroke-width:2px,color:#000
    style C fill:#e8f5e8,stroke:#333,stroke-width:2px,color:#000
    style D fill:#f3e5f5,stroke:#333,stroke-width:2px,color:#000
    style E fill:#fff3e0,stroke:#333,stroke-width:2px,color:#000
    style F fill:#e8f5e8,stroke:#333,stroke-width:2px,color:#000
    style G fill:#e1f5fe,stroke:#333,stroke-width:2px,color:#000
    style H fill:#f3e5f5,stroke:#333,stroke-width:2px,color:#000
    style I fill:#c8e6c9,stroke:#333,stroke-width:2px,color:#000
```

## 🚀 Frontend Implementation (Next.js)

### Step 1: Create API Client with Locale Support

```typescript
// src/lib/api-client.ts
import { getLocale } from 'next-intl/server';
import { headers } from 'next/headers';

interface ApiClientConfig {
  baseURL: string;
  timeout?: number;
}

class ApiClient {
  private baseURL: string;
  private timeout: number;

  constructor(config: ApiClientConfig) {
    this.baseURL = config.baseURL;
    this.timeout = config.timeout || 10000;
  }

  private async getHeaders(): Promise<HeadersInit> {
    const headersList = await headers();
    const locale = headersList.get('x-locale') || 'en';
    const cookieHeader = headersList.get('cookie') || '';

    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Accept-Language': locale,
      'X-Locale': locale,
      'Cookie': cookieHeader,
    };
  }

  async get<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'GET',
      headers: await this.getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    return response.json();
  }

  async post<T>(endpoint: string, data: any): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers: await this.getHeaders(),
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    return response.json();
  }

  async put<T>(endpoint: string, data: any): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'PUT',
      headers: await this.getHeaders(),
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    return response.json();
  }

  async delete<T>(endpoint: string): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'DELETE',
      headers: await this.getHeaders(),
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    return response.json();
  }
}

// Export singleton instance
export const apiClient = new ApiClient({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api',
});
```

### Step 2: Create Client-Side API Hook

```typescript
// src/hooks/useApiClient.ts
"use client";

import { useLocale } from 'next-intl';

interface ClientApiConfig {
  baseURL: string;
}

class ClientApiClient {
  private baseURL: string;

  constructor(config: ClientApiConfig) {
    this.baseURL = config.baseURL;
  }

  private getHeaders(locale: string): HeadersInit {
    // Get cookie value
    const getCookie = (name: string): string | null => {
      const value = `; ${document.cookie}`;
      const parts = value.split(`; ${name}=`);
      if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
      return null;
    };

    const token = getCookie('auth_token'); // If using authentication

    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Accept-Language': locale,
      'X-Locale': locale,
      ...(token && { 'Authorization': `Bearer ${token}` }),
    };
  }

  async request<T>(
    endpoint: string, 
    options: RequestInit & { locale: string }
  ): Promise<T> {
    const { locale, ...fetchOptions } = options;
    
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      ...fetchOptions,
      headers: {
        ...this.getHeaders(locale),
        ...fetchOptions.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    return response.json();
  }
}

const clientApiClient = new ClientApiClient({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api',
});

export function useApiClient() {
  const locale = useLocale();

  return {
    get: <T>(endpoint: string) => 
      clientApiClient.request<T>(endpoint, { method: 'GET', locale }),
    
    post: <T>(endpoint: string, data: any) => 
      clientApiClient.request<T>(endpoint, { 
        method: 'POST', 
        body: JSON.stringify(data),
        locale 
      }),
    
    put: <T>(endpoint: string, data: any) => 
      clientApiClient.request<T>(endpoint, { 
        method: 'PUT', 
        body: JSON.stringify(data),
        locale 
      }),
    
    delete: <T>(endpoint: string) => 
      clientApiClient.request<T>(endpoint, { method: 'DELETE', locale }),
  };
}
```

### Step 3: Usage Examples

```typescript
// Server Component Example
// src/app/[locale]/products/page.tsx
import { apiClient } from '@/lib/api-client';

interface Product {
  id: number;
  name: string;
  description: string;
  price: number;
}

export default async function ProductsPage() {
  try {
    const products = await apiClient.get<Product[]>('/products');
    
    return (
      <div>
        <h1>Products</h1>
        {products.map(product => (
          <div key={product.id}>
            <h2>{product.name}</h2>
            <p>{product.description}</p>
            <span>${product.price}</span>
          </div>
        ))}
      </div>
    );
  } catch (error) {
    return <div>Error loading products</div>;
  }
}
```

```typescript
// Client Component Example
// src/components/ProductForm.tsx
"use client";

import { useState } from 'react';
import { useApiClient } from '@/hooks/useApiClient';

export default function ProductForm() {
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);
  const apiClient = useApiClient();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      await apiClient.post('/products', { name });
      alert('Product created successfully!');
      setName('');
    } catch (error) {
      alert('Error creating product');
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="text"
        value={name}
        onChange={(e) => setName(e.target.value)}
        placeholder="Product name"
        required
      />
      <button type="submit" disabled={loading}>
        {loading ? 'Creating...' : 'Create Product'}
      </button>
    </form>
  );
}
```
