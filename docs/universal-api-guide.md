# Universal API Client Guide - Flexible & Internationalized

## 📋 Overview

This guide covers the new Universal API Client system that supports:
- **Direct API calls** to <PERSON><PERSON> backend
- **Next.js API proxy** (optional, toggleable)
- **Automatic language parameter** injection
- **Server and Client components** support
- **Type safety** with TypeScript

## 🎯 Architecture

```mermaid
graph TD
    A[Frontend Component] --> B{Environment}
    B -->|Server Component| C[serverApiClient]
    B -->|Client Component| D[apiClient / useApiClient]
    
    C --> E{API_CONFIG.USE_NEXT_API_PROXY}
    D --> E
    
    E -->|true| F[Next.js API Proxy /cpi]
    E -->|false| G[Direct API Calls]
    
    F --> H[Laravel API]
    G --> H
    
    H --> I[Response with Locale Data]
    
    style A fill:#e1f5fe,stroke:#333,stroke-width:2px,color:#000
    style B fill:#fff3e0,stroke:#333,stroke-width:2px,color:#000
    style C fill:#f3e5f5,stroke:#333,stroke-width:2px,color:#000
    style D fill:#f3e5f5,stroke:#333,stroke-width:2px,color:#000
    style E fill:#fff3e0,stroke:#333,stroke-width:2px,color:#000
    style F fill:#ffebee,stroke:#333,stroke-width:2px,color:#000
    style G fill:#e8f5e8,stroke:#333,stroke-width:2px,color:#000
    style H fill:#e1f5fe,stroke:#333,stroke-width:2px,color:#000
    style I fill:#c8e6c9,stroke:#333,stroke-width:2px,color:#000
```

## 🔧 Configuration

### Environment Variables

```bash
# Toggle between direct API calls or Next.js API proxy
NEXT_PUBLIC_USE_API_PROXY=false

# Direct API URLs (used when NEXT_PUBLIC_USE_API_PROXY=false)
NEXT_PUBLIC_BLOG_API_URL=https://api.cslant.com/api
NEXT_PUBLIC_MAIN_API_URL=https://main-api.cslant.com/api

# Frontend URL (used for Next.js API proxy)
NEXT_PUBLIC_FRONTEND_URL=http://localhost:3000
```

### API Configuration

```typescript
// src/config/api.ts
export const API_CONFIG = {
  USE_NEXT_API_PROXY: process.env.NEXT_PUBLIC_USE_API_PROXY === 'true',
  BLOG_API_URL: process.env.NEXT_PUBLIC_BLOG_API_URL || 'https://api.cslant.com/api',
  MAIN_API_URL: process.env.NEXT_PUBLIC_MAIN_API_URL || 'https://main-api.cslant.com/api',
  // ...
};
```

## 🚀 Usage

### Server Components

```typescript
// src/app/[locale]/blog/page.tsx
import { BlogService } from '@/services/apis/blog';
import { serverApiClient } from '@/lib/server-api-client';
import { getLocale } from 'next-intl/server';

export default async function BlogPage() {
  const locale = await getLocale();
  
  // Option 1: Use service (recommended)
  const { success, data: posts } = await BlogService.getPosts({
    page: 1,
    per_page: 10,
    locale: locale
  });

  // Option 2: Use serverApiClient directly
  const response = await serverApiClient.get('posts', {
    page: 1,
    per_page: 10
  }, {
    type: 'blog',
    locale: locale
  });

  return (
    <div>
      <h1>Blog Posts</h1>
      {posts?.map(post => (
        <article key={post.id}>
          <h2>{post.name}</h2>
          <p>{post.excerpt}</p>
        </article>
      ))}
    </div>
  );
}
```

### Client Components

```typescript
// src/components/BlogList.tsx
'use client';

import { useApiClient } from '@/hooks/useApiClient';
import { apiClient } from '@/lib/api-client';

export default function BlogList() {
  // Option 1: Use hook (recommended)
  const api = useApiClient({ defaultType: 'blog' });
  
  const fetchPosts = async () => {
    const response = await api.get('posts', { page: 1 });
    if (response.success) {
      setPosts(response.data);
    }
  };

  // Option 2: Use apiClient directly
  const fetchPostsDirectly = async () => {
    const response = await apiClient.get('posts', { page: 1 }, {
      type: 'blog',
      locale: 'en' // Explicit locale
    });
  };

  return (
    <div>
      {/* Component JSX */}
    </div>
  );
}
```

## 🎯 API Client Methods

### Universal Methods

All API clients support these methods:

```typescript
// GET request
const response = await api.get<T>(endpoint, params?, options?);

// POST request
const response = await api.post<T>(endpoint, data?, options?);

// PUT request
const response = await api.put<T>(endpoint, data?, options?);

// DELETE request
const response = await api.delete<T>(endpoint, options?);

// PATCH request
const response = await api.patch<T>(endpoint, data?, options?);
```

### Response Format

```typescript
interface ApiResponse<T> {
  data: T | null;
  error: any;
  meta?: any;
  success: boolean;
}
```

## 🌐 Language Handling

### Automatic Language Detection

```typescript
// Client-side: reads from cslant_locale cookie
// Server-side: uses next-intl getLocale() or x-locale header

// All API calls automatically include ?lang=en or ?lang=vi
const response = await api.get('posts'); // → /api/posts?lang=en
```

### Manual Language Override

```typescript
// Force specific language
const response = await api.get('posts', {}, {
  locale: 'vi' // Force Vietnamese
});

// Multi-language requests
const [enPosts, viPosts] = await Promise.all([
  api.get('posts', {}, { locale: 'en' }),
  api.get('posts', {}, { locale: 'vi' })
]);
```

## 🔄 Switching Between Direct API and Proxy

### Direct API Calls (Recommended)

```bash
NEXT_PUBLIC_USE_API_PROXY=false
NEXT_PUBLIC_BLOG_API_URL=https://api.cslant.com/api
```

**Benefits:**
- ✅ Faster (no proxy overhead)
- ✅ Better caching
- ✅ Direct error handling
- ✅ Simpler debugging

### Next.js API Proxy

```bash
NEXT_PUBLIC_USE_API_PROXY=true
NEXT_PUBLIC_FRONTEND_URL=http://localhost:3000
```

**Benefits:**
- ✅ CORS handling
- ✅ Request transformation
- ✅ Server-side caching
- ✅ Authentication proxy

## 📊 URL Patterns

### Direct API Mode

```
GET /posts?lang=en → https://api.cslant.com/api/posts?lang=en
POST /contact?lang=vi → https://main-api.cslant.com/api/contact?lang=vi
```

### Proxy API Mode

```
GET /posts?lang=en → http://localhost:3000/cpi/posts?lang=en
POST /contact?lang=vi → http://localhost:3000/cpi/contact?lang=vi
```

## 🎯 Best Practices

### 1. Use Services for Complex Logic

```typescript
// ✅ Good
const posts = await BlogService.getPosts({ locale });

// ❌ Avoid
const response = await apiClient.get('posts', {}, { locale });
```

### 2. Use Hooks in Client Components

```typescript
// ✅ Good
const api = useApiClient({ defaultType: 'blog' });

// ❌ Avoid
import { apiClient } from '@/lib/api-client';
```

### 3. Handle Errors Properly

```typescript
const response = await api.get('posts');
if (!response.success) {
  console.error('API Error:', response.error);
  return;
}
// Use response.data
```

### 4. Use TypeScript Types

```typescript
interface Post {
  id: number;
  name: string;
  excerpt: string;
}

const response = await api.get<Post[]>('posts');
```

## 🔧 Migration from Old API

### Before (Old Pattern)

```typescript
import { fetcher } from '@/api';

const response = await fetcher({
  endpoint: 'posts',
  type: 'blog',
  params: { page: 1 }
});
```

### After (New Pattern)

```typescript
import { useApiClient } from '@/hooks/useApiClient';

const api = useApiClient({ defaultType: 'blog' });
const response = await api.get('posts', { page: 1 });
```

## ✅ Benefits

- ✅ **Flexible**: Toggle between direct API and proxy
- ✅ **Automatic i18n**: Language parameter injection
- ✅ **Type Safe**: Full TypeScript support
- ✅ **Universal**: Works in server and client components
- ✅ **Consistent**: Same API across all environments
- ✅ **Performant**: Direct API calls when possible
- ✅ **Maintainable**: Clean separation of concerns
