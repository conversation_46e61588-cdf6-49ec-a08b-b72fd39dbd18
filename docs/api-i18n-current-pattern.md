# API Internationalization for Current CSlant Pattern

## 📋 Overview

This guide shows how to integrate language parameter (`lang`) into the **existing API pattern** in the CSlant project, working with the current `fetcher()` function and `/cpi/[...endpoint]` proxy.

## 🎯 Current Architecture

```mermaid
graph TD
    A[Frontend Component] --> B[fetcher function]
    B --> C[/cpi/endpoint proxy]
    C --> D{API Type}
    D -->|blog| E[BLOG_API_URL]
    D -->|home| F[MAIN_API_URL]
    E --> G[Laravel Backend]
    F --> G
    
    style A fill:#e1f5fe,stroke:#333,stroke-width:2px,color:#000
    style B fill:#fff3e0,stroke:#333,stroke-width:2px,color:#000
    style C fill:#f3e5f5,stroke:#333,stroke-width:2px,color:#000
    style D fill:#fff3e0,stroke:#333,stroke-width:2px,color:#000
    style E fill:#e8f5e8,stroke:#333,stroke-width:2px,color:#000
    style F fill:#e8f5e8,stroke:#333,stroke-width:2px,color:#000
    style G fill:#c8e6c9,stroke:#333,stroke-width:2px,color:#000
```

## 🔧 Frontend Implementation

### Step 1: Update Core Fetcher Function

Update `src/api/index.ts` to automatically include language parameter:

```typescript
// src/api/index.ts
import { FRONT_END_DOMAIN, ROUTE_API_HANDLER } from '@/utils/env';
import { getLocale } from 'next-intl/server';
import { headers } from 'next/headers';

export type FetchOptions<T = any> = {
  endpoint: string;
  type?: string;
  params?: Record<string, any>;
  headers?: HeadersInit;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  body?: BodyInit | null;
  next?: { revalidate: number };
  locale?: string; // Add locale option
};

function buildUrlWithParams(url: string, params?: Record<string, any>): string {
  if (!params) return url;
  const query = new URLSearchParams();
  for (const key in params) {
    if (params[key] !== undefined && params[key] !== null) {
      query.append(key, String(params[key]));
    }
  }
  return url.includes('?') ? `${url}&${query}` : `${url}?${query}`;
}

async function getLocaleFromContext(): Promise<string> {
  try {
    // Try to get locale from next-intl (server-side)
    const locale = await getLocale();
    return locale;
  } catch {
    try {
      // Fallback: get from headers (set by middleware)
      const headersList = await headers();
      return headersList.get('x-locale') || 'en';
    } catch {
      // Final fallback
      return 'en';
    }
  }
}

export async function fetcher<T = any>({
  endpoint,
  type = 'blog',
  params = {},
  headers: customHeaders = {},
  method = 'GET',
  body = null,
  next,
  locale,
}: FetchOptions): Promise<{ data: T | null; error: any; meta: any }> {
  // Get locale if not provided
  const currentLocale = locale || await getLocaleFromContext();
  
  // Add language parameter to params
  const paramsWithLang = {
    ...params,
    lang: currentLocale,
  };

  const url = buildUrlWithParams(`${FRONT_END_DOMAIN}/${ROUTE_API_HANDLER}/${endpoint}`, paramsWithLang);

  try {
    const normalizedHeaders: HeadersInit = {
      'Content-Type': 'application/json',
      ...(customHeaders instanceof Headers ? Object.fromEntries(customHeaders.entries()) : customHeaders),
      typeEndpoint: type ?? 'blog',
      'Accept-Language': currentLocale, // Add Accept-Language header
    };

    const res = await fetch(url, {
      method,
      headers: normalizedHeaders,
      body,
      ...(next ? { next } : {}),
    });

    const json = await res.json();
    if (!res.ok) {
      return { data: null, error: { message: await res.text() }, meta: null };
    }

    return { data: json?.data ?? null, meta: json?.meta ?? null, error: null };
  } catch (error) {
    return { data: null, error, meta: null };
  }
}
```

### Step 2: Create Client-Side Hook

Create `src/hooks/useApiClient.ts` for client-side components:

```typescript
// src/hooks/useApiClient.ts
'use client';

import { useLocale } from 'next-intl';
import { useMemo } from 'react';
import { FRONT_END_DOMAIN, ROUTE_API_HANDLER } from '@/utils/env';

interface ApiClientOptions {
  type?: string;
  headers?: HeadersInit;
}

class ClientApiClient {
  private locale: string;
  private baseUrl: string;

  constructor(locale: string) {
    this.locale = locale;
    this.baseUrl = `${FRONT_END_DOMAIN}/${ROUTE_API_HANDLER}`;
  }

  private buildUrlWithParams(endpoint: string, params?: Record<string, any>): string {
    const url = `${this.baseUrl}/${endpoint}`;
    
    if (!params) {
      return `${url}?lang=${this.locale}`;
    }

    const query = new URLSearchParams();
    
    // Add language parameter first
    query.append('lang', this.locale);
    
    // Add other parameters
    for (const key in params) {
      if (params[key] !== undefined && params[key] !== null) {
        query.append(key, String(params[key]));
      }
    }
    
    return `${url}?${query}`;
  }

  async get<T>(
    endpoint: string, 
    params?: Record<string, any>, 
    options: ApiClientOptions = {}
  ): Promise<{ data: T | null; error: any; meta: any }> {
    try {
      const url = this.buildUrlWithParams(endpoint, params);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept-Language': this.locale,
          typeEndpoint: options.type || 'blog',
          ...options.headers,
        },
      });

      const json = await response.json();
      
      if (!response.ok) {
        return { data: null, error: { message: json.message || 'API Error' }, meta: null };
      }

      return { data: json?.data ?? null, meta: json?.meta ?? null, error: null };
    } catch (error) {
      return { data: null, error, meta: null };
    }
  }

  async post<T>(
    endpoint: string, 
    data?: any, 
    params?: Record<string, any>,
    options: ApiClientOptions = {}
  ): Promise<{ data: T | null; error: any; meta: any }> {
    try {
      const url = this.buildUrlWithParams(endpoint, params);
      
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept-Language': this.locale,
          typeEndpoint: options.type || 'blog',
          ...options.headers,
        },
        body: data ? JSON.stringify(data) : undefined,
      });

      const json = await response.json();
      
      if (!response.ok) {
        return { data: null, error: { message: json.message || 'API Error' }, meta: null };
      }

      return { data: json?.data ?? null, meta: json?.meta ?? null, error: null };
    } catch (error) {
      return { data: null, error, meta: null };
    }
  }
}

export function useApiClient() {
  const locale = useLocale();
  
  return useMemo(() => new ClientApiClient(locale), [locale]);
}
```

### Step 3: Update Existing API Services

Update `src/api/blog/post.ts` to support locale:

```typescript
// src/api/blog/post.ts
import { fetcher } from '@/api';

export const blogPostApi = {
  get: async <T = any>({
    params,
    slug = '',
    locale, // Add locale parameter
  }: {
    params?: Record<string, string | number | string[] | number[] | undefined>;
    slug?: string;
    type?: string;
    locale?: string; // Add locale parameter
  }): Promise<{data: T | null; error: any; meta: any}> => {
    const path = slug ? `posts/${slug}` : 'posts';
    return fetcher<T>({ 
      endpoint: path, 
      params, 
      type: 'blog',
      locale // Pass locale to fetcher
    });
  }
};
```

### Step 4: Update Blog Service

Update `src/services/apis/blog.ts`:

```typescript
// src/services/apis/blog.ts
import { blogPostApi } from '@/api/blog/post';
import { IBlogPostDetail, TBlogPost } from '@/types/blog';
import { IMetaSEO } from '@/types/app';
import { BLOG_ENDPOINT } from '@/constants/endpoint';

export class BlogService {
  /**
   * Get blog posts with locale support
   */
  static async getPosts(params?: {
    page?: number;
    per_page?: number;
    category?: string;
    tag?: string;
    search?: string;
    locale?: string; // Add locale parameter
  }) {
    try {
      const response = await blogPostApi.get({
        params: {
          page: params?.page || 1,
          per_page: params?.per_page || 10,
          ...(params?.category && { category: params.category }),
          ...(params?.tag && { tag: params.tag }),
          ...(params?.search && { search: params.search }),
        },
        locale: params?.locale, // Pass locale
      });

      return {
        success: true,
        data: response.data,
        meta: response.meta,
        error: null
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        meta: null,
        error: error
      };
    }
  }

  /**
   * Get blog post by slug with locale support
   */
  static async getPostBySlug(slug: string, locale?: string): Promise<{
    success: boolean;
    data: TBlogPost | null;
    meta: any;
    error: any;
  }> {
    try {
      console.log(`[BlogService] Fetching blog post with slug: ${slug}, locale: ${locale}`);

      const response = await blogPostApi.get<TBlogPost>({
        slug: slug,
        locale: locale, // Pass locale
      });

      if (response.error) {
        console.error(`[BlogService] API Error for slug ${slug}:`, response.error);
        return {
          success: false,
          data: null,
          meta: null,
          error: response.error
        };
      }

      if (!response.data) {
        console.warn(`[BlogService] No data returned for slug: ${slug}`);
        return {
          success: false,
          data: null,
          meta: null,
          error: 'No data found'
        };
      }

      console.log(`[BlogService] Successfully fetched blog post: ${response.data.name}`);
      return {
        success: true,
        data: response.data,
        meta: response.meta,
        error: null
      };
    } catch (error) {
      console.error(`[BlogService] Exception when fetching slug ${slug}:`, error);
      return {
        success: false,
        data: null,
        meta: null,
        error: error
      };
    }
  }
}
```
