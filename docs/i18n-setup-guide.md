# Next.js Multi-Language Setup Guide với next-intl

## 📋 Thông tin phiên bản

- **Next.js**: 15.1.2
- **next-intl**: 4.1.0
- **React**: 18.3.1
- **TypeScript**: 5.4.3

## 🚀 Bước 1: Cài đặt Package

```bash
yarn add next-intl
```

## 🔧 Bước 2: <PERSON><PERSON>u hình next.config.mjs

```javascript
import createNextIntlPlugin from 'next-intl/plugin';

const withNextIntl = createNextIntlPlugin('./src/i18n.ts');

const isProd = process.env.NEXT_PUBLIC_APP_ENV === "production";
const isDev = process.env.NEXT_PUBLIC_APP_ENV === "local";
const protocol = process.env.NEXT_PUBLIC_IS_USE_HTTPS === 'true' ? 'https' : 'http';

/** @type {import('next').NextConfig} */
const nextConfig = {
  // ... other configs
};

export default withNextIntl(nextConfig);
```

## 📁 Bước 3: Tạo cấu trúc thư mục

```
src/
├── i18n.ts                    # Cấu hình i18n
├── navigation.ts              # Navigation helpers
├── middleware.ts              # Middleware xử lý locale
├── app/
│   ├── layout.tsx            # Root layout
│   ├── page.tsx              # Home page (cookie-based)
│   ├── about/page.tsx        # About page (cookie-based)
│   ├── contact/page.tsx      # Contact page (cookie-based)
│   └── [locale]/             # Dynamic locale routes
│       ├── layout.tsx        # Locale wrapper
│       ├── page.tsx          # Home page (/en, /vi)
│       ├── about/page.tsx    # About page (/en/about, /vi/about)
│       └── contact/page.tsx  # Contact page (/en/contact, /vi/contact)
└── components/
    └── Layout/
        └── LanguageSwitcher.tsx

messages/
├── en.json                   # English translations
└── vi.json                   # Vietnamese translations
```

## ⚙️ Bước 4: Cấu hình i18n.ts

```typescript
import { getRequestConfig } from 'next-intl/server';
import { headers } from 'next/headers';

export const locales = ['en', 'vi'] as const;
export const defaultLocale = 'en' as const;
export type Locale = (typeof locales)[number];

export default getRequestConfig(async ({ locale }) => {
  // If no locale is provided, try to get it from headers or use default
  if (!locale) {
    const headersList = await headers();
    const pathname = headersList.get('x-pathname') || '/';
    
    // Check if pathname starts with a locale
    const pathnameLocale = locales.find(loc => 
      pathname.startsWith(`/${loc}/`) || pathname === `/${loc}`
    );
    
    if (pathnameLocale) {
      locale = pathnameLocale;
    } else {
      // Use cookie or default
      const cookieLocale = headersList.get('x-locale') || defaultLocale;
      locale = locales.includes(cookieLocale as any) ? cookieLocale : defaultLocale;
    }
  }

  // Validate that the locale is valid
  if (!locales.includes(locale as any)) {
    locale = defaultLocale;
  }

  return {
    locale,
    messages: (await import(`../messages/${locale}.json`)).default
  };
});
```

## 🛣️ Bước 5: Cấu hình navigation.ts

```typescript
import { createNavigation } from 'next-intl/navigation';
import { locales, defaultLocale } from './i18n';

export const { Link, redirect, usePathname, useRouter } =
  createNavigation({
    locales,
    defaultLocale,
    localePrefix: 'as-needed'
  });
```

## 🔀 Bước 6: Cấu hình middleware.ts

```typescript
import createMiddleware from "next-intl/middleware";
import { NextRequest, NextResponse } from "next/server";
import { userAgent } from "next/server";
import { locales, defaultLocale } from "./i18n";

const i18nMiddleware = createMiddleware({
  locales,
  defaultLocale,
  localePrefix: 'as-needed',
  localeDetection: false
});

const checkDeviceMiddleware = (
  request: NextRequest,
  response: NextResponse,
) => {
  const { device } = userAgent(request);
  const isMobile = device.type === 'mobile';
  response.headers.set("x-device-type", isMobile ? "mobile" : "desktop");
  return response;
};

export default async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Get locale from cookie or use default
  const cookieLocale = request.cookies.get("cslant_locale")?.value ?? defaultLocale;
  
  // Determine locale based on URL path
  let locale = defaultLocale;
  
  // Check if URL starts with a locale (e.g., /en/about, /vi/contact)
  const pathnameHasLocale = locales.some(
    (loc) => pathname.startsWith(`/${loc}/`) || pathname === `/${loc}`
  );
  
  if (pathnameHasLocale) {
    // Extract locale from URL (e.g., /en/about -> en)
    locale = pathname.split('/')[1] as typeof locales[number];
  } else {
    // For paths without locale prefix (e.g., /about, /contact), use cookie locale
    locale = cookieLocale as typeof locales[number];
    if (!locales.includes(locale as any)) {
      locale = defaultLocale;
    }
  }

  // Create request with custom headers for i18n
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('x-locale', locale);
  requestHeaders.set('x-pathname', pathname);
  
  const modifiedRequest = new NextRequest(request.url, {
    headers: requestHeaders,
  });

  let response = i18nMiddleware(modifiedRequest);

  // Set locale cookie
  response.cookies.set({
    name: "cslant_locale",
    value: locale,
    path: "/",
    httpOnly: false,
    domain: process.env.NEXT_PUBLIC_COOKIE_DOMAIN,
  });

  // Set headers for the response
  response.headers.set('x-locale', locale);
  response.headers.set('x-pathname', pathname);

  // Other middleware here
  response = checkDeviceMiddleware(request, response);
  return response;
}

export const config = {
  matcher: [
    "/((?!_next|api|.*\\..*|favicon.ico).*)",
  ],
};
```

## 📄 Bước 7: Cấu hình Root Layout (src/app/layout.tsx)

```typescript
import React from "react";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { headers } from 'next/headers';
import { defaultLocale } from "@/i18n";
// ... other imports

export default async function RootLayout({ children }: Props) {
  // Get locale from headers set by middleware
  const headersList = await headers();
  const locale = headersList.get('x-locale') || defaultLocale;
  const messages = await getMessages();

  return (
    <html lang={locale}>
      <body className={poppins.className}>
        <NextIntlClientProvider messages={messages}>
          {children}
          {/* ... other components */}
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
```

## 🌐 Bước 8: Cấu hình Locale Layout (src/app/[locale]/layout.tsx)

```typescript
import React from "react";
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { locales } from '@/i18n';

type Props = {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
};

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}

export default async function LocaleLayout({
  children,
  params
}: Props) {
  const { locale } = await params;

  // Validate locale
  if (!locales.includes(locale as any)) {
    return <div>Invalid Locale</div>;
  }

  const messages = await getMessages();

  return (
    <NextIntlClientProvider messages={messages}>
      {children}
    </NextIntlClientProvider>
  );
}
```

## 🔄 Bước 9: Tạo Language Switcher Component

```typescript
"use client";

import React, { useState, useRef, useEffect } from 'react';
import { useLocale, useTranslations } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import * as Icon from "react-feather";

const LanguageSwitcher: React.FC = () => {
  const t = useTranslations('common');
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const languages = [
    { code: 'en', name: t('english'), flag: '🇺🇸' },
    { code: 'vi', name: t('vietnamese'), flag: '🇻🇳' }
  ];

  const currentLanguage = languages.find(lang => lang.code === locale) || languages[0];

  const handleLanguageChange = (newLocale: string) => {
    // Set cookie for locale preference
    document.cookie = `cslant_locale=${newLocale}; path=/; max-age=31536000`;

    // Check if current path has locale prefix
    const hasLocalePrefix = pathname.startsWith('/en/') || pathname.startsWith('/vi/') ||
                           pathname === '/en' || pathname === '/vi';

    if (hasLocalePrefix) {
      // Replace current locale with new one
      const pathWithoutLocale = pathname.replace(/^\/(en|vi)/, '') || '/';
      router.push(`/${newLocale}${pathWithoutLocale}`);
    } else {
      // For paths without locale prefix, reload to apply new locale
      window.location.reload();
    }

    setIsOpen(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="language-switcher" ref={dropdownRef}>
      <button
        className="language-btn"
        onClick={() => setIsOpen(!isOpen)}
        aria-label={t('language')}
      >
        <span className="flag">{currentLanguage.flag}</span>
        <span className="lang-code">{currentLanguage.code.toUpperCase()}</span>
        <Icon.ChevronDown size={16} />
      </button>

      {isOpen && (
        <div className="language-dropdown">
          {languages.map((language) => (
            <button
              key={language.code}
              className={`language-option ${locale === language.code ? 'active' : ''}`}
              onClick={() => handleLanguageChange(language.code)}
            >
              <span className="flag">{language.flag}</span>
              <span className="name">{language.name}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default LanguageSwitcher;
```

## 📝 Bước 10: Tạo Translation Files

### messages/en.json
```json
{
  "navigation": {
    "home": "Home",
    "about": "About",
    "contact": "Contact",
    "blog": "Blog",
    "faq": "FAQ"
  },
  "common": {
    "language": "Language",
    "english": "English",
    "vietnamese": "Vietnamese"
  },
  "pages": {
    "about": {
      "title": "About Us",
      "subtitle": "Learn more about our team"
    }
  }
}
```

### messages/vi.json
```json
{
  "navigation": {
    "home": "Trang chủ",
    "about": "Giới thiệu",
    "contact": "Liên hệ",
    "blog": "Blog",
    "faq": "Câu hỏi thường gặp"
  },
  "common": {
    "language": "Ngôn ngữ",
    "english": "Tiếng Anh",
    "vietnamese": "Tiếng Việt"
  },
  "pages": {
    "about": {
      "title": "Giới thiệu",
      "subtitle": "Tìm hiểu thêm về đội ngũ"
    }
  }
}
```

## 🎯 URL Patterns được hỗ trợ

| URL Pattern | Locale Source | Description |
|-------------|---------------|-------------|
| `/` | Cookie `cslant_locale` | Trang chủ (ngôn ngữ theo cookie) |
| `/about` | Cookie `cslant_locale` | Trang giới thiệu (ngôn ngữ theo cookie) |
| `/contact` | Cookie `cslant_locale` | Trang liên hệ (ngôn ngữ theo cookie) |
| `/en` | URL Path | Trang chủ tiếng Anh |
| `/en/about` | URL Path | Trang giới thiệu tiếng Anh |
| `/en/contact` | URL Path | Trang liên hệ tiếng Anh |
| `/vi` | URL Path | Trang chủ tiếng Việt |
| `/vi/about` | URL Path | Trang giới thiệu tiếng Việt |
| `/vi/contact` | URL Path | Trang liên hệ tiếng Việt |

## 🔧 Cách sử dụng trong Components

```typescript
import { useTranslations } from 'next-intl';

export default function AboutPage() {
  const t = useTranslations('pages.about');

  return (
    <div>
      <h1>{t('title')}</h1>
      <p>{t('subtitle')}</p>
    </div>
  );
}
```

## 🚀 Thêm ngôn ngữ mới

1. **Thêm locale vào `src/i18n.ts`:**
```typescript
export const locales = ['en', 'vi', 'ja'] as const; // Thêm 'ja'
```

2. **Tạo file translation:**
```
messages/ja.json
```

3. **Tự động có các URL:**
- `/ja` → Trang chủ tiếng Nhật
- `/ja/about` → Trang giới thiệu tiếng Nhật
- `/ja/contact` → Trang liên hệ tiếng Nhật

## ⚠️ Lưu ý quan trọng

1. **Next.js 15**: Phải await `params` trước khi sử dụng
2. **Cookie Domain**: Set `NEXT_PUBLIC_COOKIE_DOMAIN` trong env
3. **Middleware Matcher**: Exclude static files và API routes
4. **notFound()**: Không sử dụng trong root layout
5. **Headers**: Sử dụng custom headers để truyền locale giữa middleware và components

## 🔄 Flow hoạt động của cslant_locale Cookie

### BPMN Diagram
![CSlant Locale Cookie Flow](../assets/cslant-locale-flow.png)

### Chi tiết từng bước:

#### 1. **User Request (Bắt đầu)**
- User truy cập bất kỳ URL nào trên website

#### 2. **Check URL Pattern (Kiểm tra pattern URL)**
- **Có locale prefix** (`/en/*`, `/vi/*`): Chuyển đến bước 3
- **Không có locale prefix** (`/`, `/about`, `/contact`): Chuyển đến bước 4

#### 3. **Extract Locale from URL**
- Lấy locale từ URL path (ví dụ: `/en/about` → locale = `en`)

#### 4. **Check cslant_locale Cookie**
- **Cookie tồn tại**: Sử dụng giá trị cookie
- **Cookie không tồn tại**: Sử dụng `defaultLocale` (`en`)

#### 5. **Validate Locale**
- Kiểm tra locale có hợp lệ không (trong danh sách `locales`)
- **Hợp lệ**: Tiếp tục
- **Không hợp lệ**: Fallback về `defaultLocale`

#### 6. **Set Headers**
- Set `x-locale` và `x-pathname` headers cho request

#### 7. **Process i18n Middleware**
- next-intl middleware xử lý request với locale đã xác định

#### 8. **Set/Update Cookie**
- Set hoặc update `cslant_locale` cookie với giá trị locale hiện tại
- Cookie có thời hạn 1 năm (`max-age=31536000`)

#### 9. **Render Page**
- Render page với locale và translations tương ứng

#### 10. **Language Switcher (Tùy chọn)**
- **User không đổi ngôn ngữ**: Kết thúc
- **User đổi ngôn ngữ**: Chuyển đến bước 11

#### 11. **Handle Language Change**
- **URL có locale prefix**: Navigate đến `/{newLocale}/path`
- **URL không có locale prefix**: Set cookie và reload page

#### 12. **Update Cookie & Restart**
- Update `cslant_locale` cookie với locale mới
- Tạo request mới với cookie đã update
- Quay lại bước 1

### Cookie Properties:
```javascript
{
  name: "cslant_locale",
  value: "en" | "vi", // locale code
  path: "/",
  httpOnly: false,
  domain: process.env.NEXT_PUBLIC_COOKIE_DOMAIN,
  maxAge: 31536000 // 1 year
}
```

### Ví dụ thực tế:

#### Scenario 1: User truy cập `/about` lần đầu
1. URL: `/about` (không có locale prefix)
2. Cookie: không tồn tại
3. Locale: `en` (default)
4. Set cookie: `cslant_locale=en`
5. Render: Trang About tiếng Anh

#### Scenario 2: User đổi sang tiếng Việt
1. Click Language Switcher → Vietnamese
2. Set cookie: `cslant_locale=vi`
3. Reload page
4. URL: `/about` (vẫn không có locale prefix)
5. Cookie: `cslant_locale=vi`
6. Locale: `vi` (từ cookie)
7. Render: Trang About tiếng Việt

#### Scenario 3: User truy cập `/en/contact`
1. URL: `/en/contact` (có locale prefix)
2. Extract locale: `en` (từ URL)
3. Set cookie: `cslant_locale=en`
4. Render: Trang Contact tiếng Anh

#### Scenario 4: User đổi sang `/vi/contact`
1. Click Language Switcher → Vietnamese
2. Navigate: `/vi/contact`
3. Extract locale: `vi` (từ URL)
4. Set cookie: `cslant_locale=vi`
5. Render: Trang Contact tiếng Việt

## 🎉 Kết quả

- ✅ Hỗ trợ 3 pattern URL cho mỗi page
- ✅ Cookie-based locale preference
- ✅ SSR với đa ngôn ngữ
- ✅ SEO friendly
- ✅ Type safety với TypeScript
- ✅ Scalable cho việc thêm ngôn ngữ mới
- ✅ Persistent locale preference qua cookie
- ✅ Flexible routing với và không có locale prefix
```
