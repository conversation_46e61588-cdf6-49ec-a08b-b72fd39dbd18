<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0_298_1769)">
        <mask
            id="mask0_298_1769"
            style="mask-type:alpha;"
            maskUnits="userSpaceOnUse"
            x="0"
            y="0"
            width="30"
            height="30"
        >
            <circle cx="15" cy="15" r="15" fill="#C4C4C4" />
        </mask>
        <g mask="url(#mask0_298_1769)">
            <circle cx="7.86398" cy="-2.66231" r="12.4449" transform="rotate(-22 7.86398 -2.66231)" fill="#47BDFF" />
            <circle cx="22.1375" cy="32.6639" r="12.4449" transform="rotate(-22 22.1375 32.6639)" fill="#47BDFF" />
            <circle cx="-2.6629" cy="22.1365" r="12.4449" transform="rotate(-22 -2.6629 22.1365)" fill="#B476E5" />
            <circle cx="32.337" cy="7.05449" r="12.4449" transform="rotate(-22 32.337 7.05449)" fill="#B476E5" />
        </g>
    </g>
    <defs>
        <clipPath id="clip0_298_1769">
            <rect width="30" height="30" fill="white" />
        </clipPath>
    </defs>
</svg>
